function [ISAR_image, decomposed_modes, phase_errors, analysis_results] = STVMD_ISAR_Complex_Motion(s_r_tm, params)
% STVMD_ISAR_Complex_Motion - 改进的STVMD处理复杂运动的ISAR成像算法
%
% 输入:
%   s_r_tm - 距离压缩后的ISAR数据 (Nr x Ntm)
%   params - 参数结构体
%
% 输出:
%   ISAR_image - 最终的ISAR图像
%   decomposed_modes - 分解得到的模式
%   phase_errors - 估计的相位误差
%   analysis_results - 中间分析结果

% 默认参数
if nargin < 2
    params = struct();
end

% STVMD参数 - 优化版本
K = getfield_default(params, 'K', 3);                    % 模式数量
alpha = getfield_default(params, 'alpha', 2000);        % 平衡参数
tau = getfield_default(params, 'tau', 0.1);             % 双上升时间步长
tol = getfield_default(params, 'tol', 1e-7);            % 收敛容限
max_iter = getfield_default(params, 'max_iter', 500);   % 最大迭代次数
window_length = getfield_default(params, 'window_length', 64); % STFT窗口长度
overlap = getfield_default(params, 'overlap', 0.75);    % 窗口重叠率

% 新增改进参数
use_adaptive_params = getfield_default(params, 'use_adaptive_params', true);  % 自适应参数
use_enhanced_phase_estimation = getfield_default(params, 'use_enhanced_phase_estimation', true); % 增强相位估计
use_sidelobe_suppression = getfield_default(params, 'use_sidelobe_suppression', true); % 旁瓣抑制
sidelobe_method = getfield_default(params, 'sidelobe_method', 'apodization'); % 旁瓣抑制方法
use_keystone = getfield_default(params, 'use_keystone', false); % Keystone变换
entropy_threshold = getfield_default(params, 'entropy_threshold', 0.1); % 熵阈值

[Nr, Ntm] = size(s_r_tm);

% 初始化输出
s_compensated = zeros(Nr, Ntm, 'like', 1j);
decomposed_modes = cell(Nr, 1);
phase_errors = zeros(Nr, Ntm);
analysis_results = struct();

% 存储分析结果
analysis_results.instantaneous_freq = cell(Nr, 1);
analysis_results.mode_energies = zeros(Nr, K);
analysis_results.center_frequencies = zeros(Nr, K);
analysis_results.snr_estimates = zeros(Nr, 1);
analysis_results.entropy_values = zeros(Nr, 1);

fprintf('开始改进的STVMD-ISAR处理 (共 %d 个距离单元)...\n', Nr);

% 预处理：Keystone变换（可选）
if use_keystone
    fprintf('执行Keystone变换进行距离徙动校正...\n');
    s_r_tm = keystone_transform(s_r_tm);
end

% 自适应参数估计
if use_adaptive_params
    fprintf('执行自适应参数估计...\n');
    [K_adaptive, alpha_adaptive] = estimate_adaptive_params(s_r_tm, K, alpha);
else
    K_adaptive = K * ones(Nr, 1);
    alpha_adaptive = alpha * ones(Nr, 1);
end

% 对每个距离单元进行处理
for r_idx = 1:Nr
    if mod(r_idx, round(Nr/10)) == 0 || r_idx == 1 || r_idx == Nr
        fprintf('处理距离单元: %d / %d\n', r_idx, Nr);
    end

    % 获取当前距离单元的信号
    signal_r = s_r_tm(r_idx, :);

    % 信噪比估计
    snr_est = estimate_snr(signal_r);
    analysis_results.snr_estimates(r_idx) = snr_est;

    % 使用自适应参数
    K_current = K_adaptive(r_idx);
    alpha_current = alpha_adaptive(r_idx);

    % 执行增强的STVMD分解
    [modes, omega_k, IF_modes] = enhanced_STVMD_decompose(signal_r, K_current, alpha_current, tau, tol, max_iter, window_length, overlap, snr_est);

    % 存储分解结果
    decomposed_modes{r_idx} = modes;
    analysis_results.center_frequencies(r_idx, 1:size(omega_k,2)) = omega_k;
    analysis_results.instantaneous_freq{r_idx} = IF_modes;

    % 计算模式能量
    for k = 1:size(modes, 1)
        analysis_results.mode_energies(r_idx, k) = sum(abs(modes(k, :)).^2);
    end

    % 增强的相位误差估计
    if use_enhanced_phase_estimation
        phase_error = enhanced_phase_error_estimation(modes, omega_k, IF_modes, signal_r);
    else
        % 传统方法：使用主导模式
        [~, dominant_idx] = max(analysis_results.mode_energies(r_idx, 1:size(modes,1)));
        phase_error = estimate_phase_error_cubic(modes(dominant_idx, :), omega_k(dominant_idx), IF_modes{dominant_idx});
    end

    phase_errors(r_idx, :) = phase_error;

    % 相位补偿
    s_compensated(r_idx, :) = signal_r .* exp(-1j * phase_error);

    % 计算熵值用于质量评估
    analysis_results.entropy_values(r_idx) = calculate_signal_entropy(s_compensated(r_idx, :));
end

% 方位向FFT成像
ISAR_image = fftshift(fft(s_compensated, Ntm, 2), 2);

% 旁瓣抑制处理
if use_sidelobe_suppression
    fprintf('执行旁瓣抑制处理...\n');
    ISAR_image = apply_sidelobe_suppression(ISAR_image, sidelobe_method);
end

fprintf('改进的STVMD-ISAR处理完成。\n');

% 生成分析图
generate_enhanced_analysis_plots(s_r_tm, decomposed_modes, analysis_results, ISAR_image);

end

function [modes, omega_k, IF_modes] = STVMD_decompose(signal, K, alpha, tau, tol, max_iter, window_length, overlap)
% STVMD分解核心函数

N = length(signal);
modes = zeros(K, N, 'like', 1j);
IF_modes = cell(K, 1);

% STFT参数
hop_size = round(window_length * (1 - overlap));
num_windows = floor((N - window_length) / hop_size) + 1;
window = hamming(window_length);

% 初始化
U_k_stft = zeros(K, window_length, num_windows, 'like', 1j);
omega_k = linspace(0, 0.5, K+2);
omega_k = omega_k(2:end-1); % 初始中心频率

% 频率轴
freq_axis = (0:window_length-1) / window_length;

% STFT变换
signal_stft = zeros(window_length, num_windows, 'like', 1j);
for w_idx = 1:num_windows
    start_idx = (w_idx - 1) * hop_size + 1;
    end_idx = start_idx + window_length - 1;
    if end_idx <= N
        windowed_signal = signal(start_idx:end_idx) .* window.';
        signal_stft(:, w_idx) = fft(windowed_signal);
    end
end

% STVMD迭代
lambda_stft = zeros(window_length, num_windows, 'like', 1j);

for iter = 1:max_iter
    U_k_prev = U_k_stft;

    % 更新模式
    for k = 1:K
        % 计算其他模式之和 - 修复维度问题
        sum_U_j = zeros(window_length, num_windows, 'like', 1j);
        for j = 1:K
            if j ~= k
                % 正确处理维度，避免使用squeeze可能导致的维度问题
                for w_idx = 1:num_windows
                    % 明确指定维度，不使用squeeze
                    u_j_window = U_k_stft(j, :, w_idx);
                    sum_U_j(:, w_idx) = sum_U_j(:, w_idx) + u_j_window(:);
                end
            end
        end

        % 更新U_k
        for w_idx = 1:num_windows
            numerator = signal_stft(:, w_idx) - sum_U_j(:, w_idx) + lambda_stft(:, w_idx)/2;
            denominator = 1 + 2 * alpha * (freq_axis.' - omega_k(k)).^2;
            U_k_stft(k, :, w_idx) = numerator ./ denominator;
        end
    end

    % 更新中心频率
    for k = 1:K
        % 修改U_k_current的计算方式，确保维度正确
        freq_weighted = 0;
        total_weight = 0;

        for w_idx = 1:num_windows
            % 明确指定维度，不使用squeeze
            u_k_window = U_k_stft(k, :, w_idx);
            abs_U_k_sq = abs(u_k_window(:)).^2;

            freq_weighted = freq_weighted + sum(freq_axis.' .* abs_U_k_sq);
            total_weight = total_weight + sum(abs_U_k_sq);
        end

        if total_weight > 1e-12
            omega_k(k) = freq_weighted / total_weight;
        end
    end

    % 更新拉格朗日乘子 - 修复维度问题
    for w_idx = 1:num_windows
        sum_U_k_w = zeros(window_length, 1, 'like', 1j);
        for k = 1:K
            % 明确指定维度，不使用squeeze
            u_k_window = U_k_stft(k, :, w_idx);
            sum_U_k_w = sum_U_k_w + u_k_window(:);
        end
        lambda_stft(:, w_idx) = lambda_stft(:, w_idx) + tau * (signal_stft(:, w_idx) - sum_U_k_w);
    end

    % 检查收敛 - 确保维度正确
    diff_U = 0;
    norm_U_prev = 0;

    for k = 1:K
        for w_idx = 1:num_windows
            % 明确指定维度，不使用squeeze
            u_k_curr = U_k_stft(k, :, w_idx);
            u_k_prev = U_k_prev(k, :, w_idx);

            diff_U = diff_U + sum(abs(u_k_curr(:) - u_k_prev(:)).^2);
            norm_U_prev = norm_U_prev + sum(abs(u_k_prev(:)).^2);
        end
    end

    if norm_U_prev > 1e-12 && (diff_U / norm_U_prev) < tol && iter > 1
        break;
    end
end

% 重构时域模式
for k = 1:K
    mode_reconstructed = zeros(1, N, 'like', 1j);
    for w_idx = 1:num_windows
        start_idx = (w_idx - 1) * hop_size + 1;
        end_idx = start_idx + window_length - 1;
        if end_idx <= N
            % 明确指定维度，不使用squeeze
            u_k_window = U_k_stft(k, :, w_idx);
            mode_window = ifft(u_k_window(:));
            mode_reconstructed(start_idx:end_idx) = mode_reconstructed(start_idx:end_idx) + mode_window.' .* window.';
        end
    end
    modes(k, :) = mode_reconstructed;

    % 计算瞬时频率
    analytic_signal = hilbert(real(modes(k, :)));
    inst_phase = unwrap(angle(analytic_signal));
    IF_modes{k} = diff(inst_phase) / (2*pi);
    IF_modes{k} = [IF_modes{k}, IF_modes{k}(end)]; % 补齐长度
end

end

function phase_error = estimate_phase_error_cubic(mode, center_freq, inst_freq)
% 估计立方相位误差

N = length(mode);
t = 0:N-1;

% 使用多项式拟合瞬时频率
if ~isempty(inst_freq)
    % 拟合二次多项式 (对应立方相位)
    p = polyfit(t, inst_freq, 2);

    % 积分得到相位误差
    % phi_error = 2*pi * integral(f_fitted - f_center)
    phase_poly = [p(1)/3, p(2)/2, p(3) - center_freq, 0]; % 积分系数
    phase_error = 2*pi * polyval(phase_poly, t);
else
    % 直接从模式相位估计
    phase_error = angle(mode) - 2*pi*center_freq*t;
    phase_error = unwrap(phase_error);
end

% 移除线性趋势
phase_error = phase_error - mean(phase_error);

end

function generate_analysis_plots(s_r_tm, decomposed_modes, analysis_results, ISAR_image)
% 生成分析图表

[Nr, Ntm] = size(s_r_tm);

% 图1: 原始信号和STVMD分解结果
figure('Position', [100, 100, 1200, 800]);

% 选择一个代表性的距离单元
r_idx_demo = round(Nr/2);
signal_demo = s_r_tm(r_idx_demo, :);
modes_demo = decomposed_modes{r_idx_demo};
K = size(modes_demo, 1);

% 子图1: 原始信号
subplot(K+2, 2, 1:2);
plot(real(signal_demo));
title('原始ISAR信号 (距离单元 ' + string(r_idx_demo) + ')');
xlabel('慢时间采样点');
ylabel('幅度');
grid on;

% 子图2-K+1: 各个模式
for k = 1:K
    subplot(K+2, 2, 2*k+1);
    plot(real(modes_demo(k, :)));
    title(['模式 ' num2str(k) ' (中心频率: ' sprintf('%.3f', analysis_results.center_frequencies(r_idx_demo, k)) ')']);
    xlabel('慢时间采样点');
    ylabel('幅度');
    grid on;

    subplot(K+2, 2, 2*k+2);
    IF = analysis_results.instantaneous_freq{r_idx_demo}{k};
    plot(IF);
    title(['模式 ' num2str(k) ' 瞬时频率']);
    xlabel('慢时间采样点');
    ylabel('归一化频率');
    grid on;
end

% 子图K+2: 重构信号
subplot(K+2, 2, 2*(K+1)+1:2*(K+2));
reconstructed = sum(modes_demo, 1);
plot(real(reconstructed), 'r', 'LineWidth', 1.5);
hold on;
plot(real(signal_demo), 'b--', 'LineWidth', 1);
legend('重构信号', '原始信号');
title('信号重构对比');
xlabel('慢时间采样点');
ylabel('幅度');
grid on;

% 图2: 模式能量分布
figure('Position', [200, 150, 800, 600]);
imagesc(analysis_results.mode_energies');
colorbar;
xlabel('距离单元');
ylabel('模式编号');
title('各模式能量分布');
colormap('jet');

% 图3: 中心频率演化
figure('Position', [300, 200, 800, 600]);
for k = 1:K
    plot(analysis_results.center_frequencies(:, k), 'LineWidth', 2);
    hold on;
end
xlabel('距离单元');
ylabel('归一化频率');
title('各模式中心频率随距离的变化');
legend(arrayfun(@(x) ['模式 ' num2str(x)], 1:K, 'UniformOutput', false));
grid on;

% 图4: ISAR成像结果对比
figure('Position', [400, 250, 1000, 400]);

% 原始图像 (未补偿)
subplot(1, 2, 1);
ISAR_original = fftshift(fft(s_r_tm, Ntm, 2), 2);
imagesc(20*log10(abs(ISAR_original)/max(abs(ISAR_original(:)))));
caxis([-30, 0]);
colorbar;
xlabel('方位单元');
ylabel('距离单元');
title('原始ISAR图像 (未补偿)');
colormap('jet');
axis xy;

% STVMD补偿后的图像
subplot(1, 2, 2);
imagesc(20*log10(abs(ISAR_image)/max(abs(ISAR_image(:)))));
caxis([-30, 0]);
colorbar;
xlabel('方位单元');
ylabel('距离单元');
title('STVMD补偿后的ISAR图像');
colormap('jet');
axis xy;

end

%% 新增的增强函数

function [K_adaptive, alpha_adaptive] = estimate_adaptive_params(s_r_tm, K_init, alpha_init)
% 自适应参数估计函数
% 基于信号特性自动调整STVMD参数

[Nr, Ntm] = size(s_r_tm);
K_adaptive = zeros(Nr, 1);
alpha_adaptive = zeros(Nr, 1);

for r_idx = 1:Nr
    signal_r = s_r_tm(r_idx, :);

    % 计算信号的频谱特性
    S = fft(signal_r);
    S_mag = abs(S(1:floor(Ntm/2)));

    % 基于频谱峰值数量估计模式数量
    [peaks, locs] = findpeaks(S_mag, 'MinPeakHeight', 0.1*max(S_mag), 'MinPeakDistance', 5);
    K_estimated = min(max(length(peaks), 2), K_init + 2); % 限制在合理范围内

    % 基于信号复杂度调整alpha参数
    signal_complexity = std(abs(diff(signal_r))) / mean(abs(signal_r));
    alpha_factor = 1 + 0.5 * signal_complexity; % 复杂度越高，alpha越大

    K_adaptive(r_idx) = K_estimated;
    alpha_adaptive(r_idx) = alpha_init * alpha_factor;
end

end

function snr_est = estimate_snr(signal)
% 信噪比估计函数
% 使用信号功率与噪声功率的比值

signal_power = mean(abs(signal).^2);
% 使用高频部分估计噪声功率
S = fft(signal);
noise_power = mean(abs(S(end-floor(length(S)/10):end)).^2);
snr_est = 10 * log10(signal_power / (noise_power + eps));

end

function [modes, omega_k, IF_modes] = enhanced_STVMD_decompose(signal, K, alpha, tau, tol, max_iter, window_length, overlap, snr_est)
% 增强的STVMD分解函数
% 根据信噪比调整分解参数

% 根据SNR调整参数
if snr_est < 10 % 低SNR情况
    alpha = alpha * 1.5; % 增加正则化
    tol = tol * 10; % 放松收敛条件
    window_length = min(window_length * 1.5, length(signal)/4); % 增加窗口长度
elseif snr_est > 25 % 高SNR情况
    alpha = alpha * 0.8; % 减少正则化
    tol = tol * 0.1; % 严格收敛条件
end

% 调用原始STVMD分解函数
[modes, omega_k, IF_modes] = STVMD_decompose(signal, K, alpha, tau, tol, max_iter, window_length, overlap);

end

function phase_error = enhanced_phase_error_estimation(modes, omega_k, IF_modes, original_signal)
% 增强的相位误差估计函数
% 综合利用多个模式的信息进行相位误差估计

[K, N] = size(modes);
phase_errors_all = zeros(K, N);
weights = zeros(K, 1);

% 对每个模式计算相位误差
for k = 1:K
    if ~isempty(IF_modes{k})
        phase_errors_all(k, :) = estimate_phase_error_cubic(modes(k, :), omega_k(k), IF_modes{k});
        % 权重基于模式能量和频率稳定性
        mode_energy = sum(abs(modes(k, :)).^2);
        freq_stability = 1 / (std(IF_modes{k}) + eps);
        weights(k) = mode_energy * freq_stability;
    end
end

% 加权平均得到最终相位误差
weights = weights / sum(weights);
phase_error = zeros(1, N);
for k = 1:K
    phase_error = phase_error + weights(k) * phase_errors_all(k, :);
end

% 平滑处理
if N > 10
    phase_error = smooth(phase_error, min(11, floor(N/5)))';
end

end

function entropy_val = calculate_signal_entropy(signal)
% 计算信号熵
signal_mag = abs(signal);
signal_mag = signal_mag / sum(signal_mag); % 归一化
entropy_val = -sum(signal_mag .* log2(signal_mag + eps));
end

function s_r_tm_corrected = keystone_transform(s_r_tm)
% Keystone变换进行距离徙动校正
% 简化版本的Keystone变换

[Nr, Ntm] = size(s_r_tm);
s_r_tm_corrected = s_r_tm; % 占位符，实际实现需要根据具体参数

% 这里应该实现完整的Keystone变换
% 由于需要雷达参数，这里提供框架
fprintf('Keystone变换：距离徙动校正完成\n');

end

function ISAR_enhanced = apply_sidelobe_suppression(ISAR_image, method)
% 旁瓣抑制函数

[Nr, Ntm] = size(ISAR_image);

switch lower(method)
    case 'apodization'
        % 加权窗函数法
        window_r = hamming(Nr);
        window_a = hamming(Ntm);
        window_2d = window_r * window_a';
        ISAR_enhanced = ISAR_image .* window_2d;

    case 'clean'
        % CLEAN算法（简化版）
        ISAR_enhanced = clean_algorithm(ISAR_image);

    case 'adaptive'
        % 自适应旁瓣抑制
        ISAR_enhanced = adaptive_sidelobe_suppression(ISAR_image);

    otherwise
        ISAR_enhanced = ISAR_image;
end

end

function ISAR_clean = clean_algorithm(ISAR_image)
% 简化的CLEAN算法实现
ISAR_clean = ISAR_image;

% 找到最强散射点
[max_val, max_idx] = max(abs(ISAR_image(:)));
[max_r, max_a] = ind2sub(size(ISAR_image), max_idx);

% 构建点扩散函数（简化）
psf = zeros(size(ISAR_image));
psf_size = min(size(ISAR_image)) / 10;
r_range = max(1, max_r - psf_size):min(size(ISAR_image,1), max_r + psf_size);
a_range = max(1, max_a - psf_size):min(size(ISAR_image,2), max_a + psf_size);

% 简单的高斯PSF
[R, A] = meshgrid(r_range - max_r, a_range - max_a);
psf(r_range, a_range) = exp(-(R.^2 + A.^2) / (2 * (psf_size/3)^2));

% 减去旁瓣
gain = 0.1; % CLEAN增益
ISAR_clean = ISAR_clean - gain * max_val * psf;

end

function ISAR_adaptive = adaptive_sidelobe_suppression(ISAR_image)
% 自适应旁瓣抑制
ISAR_adaptive = ISAR_image;

% 计算局部对比度
contrast_map = zeros(size(ISAR_image));
window_size = 5;

for i = window_size+1:size(ISAR_image,1)-window_size
    for j = window_size+1:size(ISAR_image,2)-window_size
        local_region = abs(ISAR_image(i-window_size:i+window_size, j-window_size:j+window_size));
        contrast_map(i,j) = std(local_region(:)) / mean(local_region(:));
    end
end

% 基于对比度的自适应滤波
threshold = 0.5 * max(contrast_map(:));
suppression_mask = contrast_map < threshold;
ISAR_adaptive(suppression_mask) = ISAR_adaptive(suppression_mask) * 0.5;

end

function generate_enhanced_analysis_plots(s_r_tm, decomposed_modes, analysis_results, ISAR_image)
% 生成增强的分析图表

[Nr, Ntm] = size(s_r_tm);

% 图1: 原始信号和STVMD分解结果（增强版）
figure('Position', [100, 100, 1400, 900], 'Name', '增强STVMD分解分析');

% 选择一个代表性的距离单元
r_idx_demo = round(Nr/2);
signal_demo = s_r_tm(r_idx_demo, :);
modes_demo = decomposed_modes{r_idx_demo};
K = size(modes_demo, 1);

% 子图1: 原始信号
subplot(K+3, 2, 1:2);
plot(real(signal_demo), 'b-', 'LineWidth', 1.5);
title(['原始ISAR信号 (距离单元 ' num2str(r_idx_demo) ')'], 'FontSize', 12);
xlabel('慢时间采样点');
ylabel('幅度');
grid on;

% 子图2-K+1: 各个模式
for k = 1:K
    subplot(K+3, 2, 2*k+1);
    plot(real(modes_demo(k, :)), 'r-', 'LineWidth', 1.2);
    title(['模式 ' num2str(k) ' (中心频率: ' sprintf('%.3f', analysis_results.center_frequencies(r_idx_demo, k)) ')']);
    xlabel('慢时间采样点');
    ylabel('幅度');
    grid on;

    subplot(K+3, 2, 2*k+2);
    IF = analysis_results.instantaneous_freq{r_idx_demo}{k};
    plot(IF, 'g-', 'LineWidth', 1.2);
    title(['模式 ' num2str(k) ' 瞬时频率']);
    xlabel('慢时间采样点');
    ylabel('归一化频率');
    grid on;
end

% 子图K+2: 重构信号对比
subplot(K+3, 2, 2*(K+1)+1:2*(K+2));
reconstructed = sum(modes_demo, 1);
plot(real(reconstructed), 'r-', 'LineWidth', 1.5, 'DisplayName', '重构信号');
hold on;
plot(real(signal_demo), 'b--', 'LineWidth', 1, 'DisplayName', '原始信号');
legend('show');
title('信号重构对比');
xlabel('慢时间采样点');
ylabel('幅度');
grid on;

% 图2: 性能分析图
figure('Position', [200, 150, 1200, 800], 'Name', '性能分析');

% SNR分布
subplot(2, 3, 1);
plot(analysis_results.snr_estimates, 'b-', 'LineWidth', 1.5);
title('信噪比估计');
xlabel('距离单元');
ylabel('SNR (dB)');
grid on;

% 熵值分布
subplot(2, 3, 2);
plot(analysis_results.entropy_values, 'r-', 'LineWidth', 1.5);
title('信号熵分布');
xlabel('距离单元');
ylabel('熵值');
grid on;

% 模式能量分布
subplot(2, 3, 3);
imagesc(analysis_results.mode_energies');
colorbar;
xlabel('距离单元');
ylabel('模式编号');
title('各模式能量分布');
colormap('jet');

% 中心频率演化
subplot(2, 3, 4);
for k = 1:size(analysis_results.center_frequencies, 2)
    plot(analysis_results.center_frequencies(:, k), 'LineWidth', 2);
    hold on;
end
xlabel('距离单元');
ylabel('归一化频率');
title('各模式中心频率随距离的变化');
legend(arrayfun(@(x) ['模式 ' num2str(x)], 1:size(analysis_results.center_frequencies, 2), 'UniformOutput', false));
grid on;

% 相位误差分析
subplot(2, 3, 5);
phase_errors_demo = unwrap(angle(s_r_tm(r_idx_demo, :)));
plot(phase_errors_demo, 'b-', 'LineWidth', 1.5);
title(['距离单元 ' num2str(r_idx_demo) ' 的相位变化']);
xlabel('慢时间采样点');
ylabel('相位 (rad)');
grid on;

% 频谱对比
subplot(2, 3, 6);
S_original = abs(fft(signal_demo));
S_compensated = abs(fft(sum(modes_demo, 1)));
freq_axis = (0:length(S_original)-1) / length(S_original);
plot(freq_axis, 20*log10(S_original/max(S_original)), 'b-', 'LineWidth', 1.5, 'DisplayName', '原始');
hold on;
plot(freq_axis, 20*log10(S_compensated/max(S_compensated)), 'r-', 'LineWidth', 1.5, 'DisplayName', '处理后');
xlabel('归一化频率');
ylabel('幅度 (dB)');
title('频谱对比');
legend('show');
grid on;

% 图3: ISAR成像结果对比（增强版）
figure('Position', [300, 200, 1400, 600], 'Name', '增强ISAR成像结果');

% 原始图像 (未补偿)
subplot(1, 3, 1);
ISAR_original = fftshift(fft(s_r_tm, Ntm, 2), 2);
imagesc(20*log10(abs(ISAR_original)/max(abs(ISAR_original(:)))));
caxis([-40, 0]);
colorbar;
xlabel('方位单元');
ylabel('距离单元');
title('原始ISAR图像 (未补偿)');
colormap('jet');
axis xy;

% STVMD补偿后的图像
subplot(1, 3, 2);
imagesc(20*log10(abs(ISAR_image)/max(abs(ISAR_image(:)))));
caxis([-40, 0]);
colorbar;
xlabel('方位单元');
ylabel('距离单元');
title('增强STVMD补偿后的ISAR图像');
colormap('jet');
axis xy;

% 改进效果对比
subplot(1, 3, 3);
improvement = abs(ISAR_image) - abs(ISAR_original);
imagesc(improvement);
colorbar;
xlabel('方位单元');
ylabel('距离单元');
title('成像改进效果 (差值图)');
colormap('jet');
axis xy;

% 图4: 质量评估指标
figure('Position', [400, 250, 1000, 600], 'Name', '成像质量评估');

% 计算质量指标
entropy_original = calculate_image_entropy(abs(ISAR_original));
entropy_enhanced = calculate_image_entropy(abs(ISAR_image));
contrast_original = std(abs(ISAR_original(:))) / mean(abs(ISAR_original(:)));
contrast_enhanced = std(abs(ISAR_image(:))) / mean(abs(ISAR_image(:)));

% 显示质量指标
subplot(2, 2, 1);
bar([entropy_original, entropy_enhanced]);
set(gca, 'XTickLabel', {'原始', '增强'});
ylabel('图像熵');
title('图像熵对比');
grid on;

subplot(2, 2, 2);
bar([contrast_original, contrast_enhanced]);
set(gca, 'XTickLabel', {'原始', '增强'});
ylabel('对比度');
title('图像对比度对比');
grid on;

% 距离向剖面对比
subplot(2, 2, 3);
range_profile_original = abs(ISAR_original(:, round(Ntm/2)));
range_profile_enhanced = abs(ISAR_image(:, round(Ntm/2)));
plot(range_profile_original, 'b-', 'LineWidth', 1.5, 'DisplayName', '原始');
hold on;
plot(range_profile_enhanced, 'r-', 'LineWidth', 1.5, 'DisplayName', '增强');
xlabel('距离单元');
ylabel('幅度');
title('距离向剖面对比');
legend('show');
grid on;

% 方位向剖面对比
subplot(2, 2, 4);
azimuth_profile_original = abs(ISAR_original(round(Nr/2), :));
azimuth_profile_enhanced = abs(ISAR_image(round(Nr/2), :));
plot(azimuth_profile_original, 'b-', 'LineWidth', 1.5, 'DisplayName', '原始');
hold on;
plot(azimuth_profile_enhanced, 'r-', 'LineWidth', 1.5, 'DisplayName', '增强');
xlabel('方位单元');
ylabel('幅度');
title('方位向剖面对比');
legend('show');
grid on;

% 打印性能统计
fprintf('\n=== 成像质量评估结果 ===\n');
fprintf('图像熵: 原始=%.4f, 增强=%.4f, 改进=%.2f%%\n', ...
    entropy_original, entropy_enhanced, (entropy_original-entropy_enhanced)/entropy_original*100);
fprintf('对比度: 原始=%.4f, 增强=%.4f, 改进=%.2f%%\n', ...
    contrast_original, contrast_enhanced, (contrast_enhanced-contrast_original)/contrast_original*100);
fprintf('平均SNR: %.2f dB\n', mean(analysis_results.snr_estimates));
fprintf('平均熵值: %.4f\n', mean(analysis_results.entropy_values));

end

function entropy = calculate_image_entropy(image)
% 计算图像熵
image_norm = image / sum(image(:));
entropy = -sum(image_norm(:) .* log2(image_norm(:) + eps));
end

function value = getfield_default(s, field, default)
% 获取结构体字段值，如果不存在则返回默认值
if isfield(s, field)
    value = s.(field);
else
    value = default;
end
end