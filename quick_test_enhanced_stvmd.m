% 快速测试增强STVMD-ISAR算法
% 用于验证算法改进效果

clear; clc; close all;

fprintf('=== 增强STVMD-ISAR算法快速测试 ===\n\n');

%% 1. 生成测试数据
fprintf('1. 生成测试数据...\n');

% 使用仿真数据
try
    ISARrot_trans;
    load('ISAR_data.mat', 's_r_tm');
    fprintf('   成功加载仿真数据\n');
catch
    % 如果没有仿真数据，生成简单的测试数据
    fprintf('   生成简单测试数据\n');
    Nr = 64; Ntm = 128;
    t = linspace(0, 1, Ntm);
    r = linspace(0, 1, Nr);
    
    % 创建包含复杂运动的测试信号
    s_r_tm = zeros(Nr, Ntm);
    for i = 1:Nr
        % 基础信号
        signal = exp(1j * 2 * pi * 10 * t);
        % 添加相位误差（模拟复杂运动）
        phase_error = 0.5 * t.^2 + 0.1 * t.^3 + 0.05 * sin(20 * pi * t);
        signal = signal .* exp(1j * phase_error);
        s_r_tm(i, :) = signal;
    end
end

[Nr, Ntm] = size(s_r_tm);
fprintf('   数据尺寸: %d x %d\n', Nr, Ntm);

% 添加噪声
SNR_dB = 15;
noise_power = var(s_r_tm(:)) / (10^(SNR_dB/10));
noise = sqrt(noise_power/2) * (randn(size(s_r_tm)) + 1j*randn(size(s_r_tm)));
s_r_tm_noisy = s_r_tm + noise;
fprintf('   添加噪声，SNR = %d dB\n', SNR_dB);

%% 2. 原始FFT成像（基准）
fprintf('\n2. 原始FFT成像...\n');
tic;
ISAR_original = fftshift(fft(s_r_tm_noisy, Ntm, 2), 2);
time_original = toc;
fprintf('   处理时间: %.3f 秒\n', time_original);

%% 3. 增强STVMD-ISAR处理
fprintf('\n3. 增强STVMD-ISAR处理...\n');

% 设置增强参数
params = struct();
params.K = 3;                                    % 模式数量
params.alpha = 2000;                            % 平衡参数
params.tau = 0.1;                               % 双上升时间步长
params.tol = 1e-7;                              % 收敛容限
params.max_iter = 300;                          % 最大迭代次数
params.window_length = 64;                      % STFT窗口长度
params.overlap = 0.75;                          % 窗口重叠率

% 启用增强功能
params.use_adaptive_params = true;              % 自适应参数
params.use_enhanced_phase_estimation = true;    % 增强相位估计
params.use_sidelobe_suppression = true;         % 旁瓣抑制
params.sidelobe_method = 'adaptive';            % 旁瓣抑制方法
params.use_keystone = false;                    % Keystone变换
params.entropy_threshold = 0.1;                 % 熵阈值

fprintf('   参数设置完成\n');
fprintf('   - 模式数量: %d\n', params.K);
fprintf('   - 自适应参数: %s\n', string(params.use_adaptive_params));
fprintf('   - 增强相位估计: %s\n', string(params.use_enhanced_phase_estimation));
fprintf('   - 旁瓣抑制: %s (%s)\n', string(params.use_sidelobe_suppression), params.sidelobe_method);

tic;
try
    [ISAR_enhanced, decomposed_modes, phase_errors, analysis_results] = ...
        STVMD_ISAR_Complex_Motion(s_r_tm_noisy, params);
    time_enhanced = toc;
    fprintf('   处理完成，耗时: %.3f 秒\n', time_enhanced);
    success = true;
catch ME
    fprintf('   处理出错: %s\n', ME.message);
    success = false;
    time_enhanced = 0;
end

%% 4. 性能评估
if success
    fprintf('\n4. 性能评估...\n');
    
    % 计算性能指标
    entropy_original = calculate_image_entropy(abs(ISAR_original));
    entropy_enhanced = calculate_image_entropy(abs(ISAR_enhanced));
    
    contrast_original = calculate_image_contrast(abs(ISAR_original));
    contrast_enhanced = calculate_image_contrast(abs(ISAR_enhanced));
    
    % 简化的旁瓣评估
    max_original = max(abs(ISAR_original(:)));
    max_enhanced = max(abs(ISAR_enhanced(:)));
    
    fprintf('   性能对比结果:\n');
    fprintf('   - 原始图像熵: %.4f\n', entropy_original);
    fprintf('   - 增强后图像熵: %.4f\n', entropy_enhanced);
    fprintf('   - 熵减少: %.2f%%\n', (entropy_original - entropy_enhanced)/entropy_original * 100);
    fprintf('   - 原始对比度: %.4f\n', contrast_original);
    fprintf('   - 增强后对比度: %.4f\n', contrast_enhanced);
    fprintf('   - 对比度提升: %.2f%%\n', (contrast_enhanced - contrast_original)/contrast_original * 100);
    fprintf('   - 处理时间比: %.2fx\n', time_enhanced / time_original);
    
    % 显示自适应参数统计
    if isfield(analysis_results, 'snr_estimates')
        fprintf('   - 平均SNR估计: %.2f dB\n', mean(analysis_results.snr_estimates));
        fprintf('   - 平均信号熵: %.4f\n', mean(analysis_results.entropy_values));
    end
    
    %% 5. 可视化结果
    fprintf('\n5. 生成对比图...\n');
    
    % 图1: 成像结果对比
    figure('Position', [100, 100, 1000, 400], 'Name', '快速测试结果对比');
    
    subplot(1, 2, 1);
    imagesc(20*log10(abs(ISAR_original)/max(abs(ISAR_original(:)))));
    caxis([-40, 0]);
    colorbar;
    title('原始FFT成像');
    xlabel('方位单元');
    ylabel('距离单元');
    colormap('jet');
    axis xy;
    
    subplot(1, 2, 2);
    imagesc(20*log10(abs(ISAR_enhanced)/max(abs(ISAR_enhanced(:)))));
    caxis([-40, 0]);
    colorbar;
    title('增强STVMD成像');
    xlabel('方位单元');
    ylabel('距离单元');
    colormap('jet');
    axis xy;
    
    % 图2: 性能指标对比
    figure('Position', [200, 150, 800, 300], 'Name', '性能指标对比');
    
    subplot(1, 3, 1);
    bar([entropy_original, entropy_enhanced]);
    set(gca, 'XTickLabel', {'原始', '增强'});
    ylabel('图像熵');
    title('图像熵对比');
    grid on;
    
    subplot(1, 3, 2);
    bar([contrast_original, contrast_enhanced]);
    set(gca, 'XTickLabel', {'原始', '增强'});
    ylabel('对比度');
    title('对比度对比');
    grid on;
    
    subplot(1, 3, 3);
    bar([time_original, time_enhanced]);
    set(gca, 'XTickLabel', {'原始', '增强'});
    ylabel('处理时间 (秒)');
    title('处理时间对比');
    grid on;
    
    % 图3: 剖面对比
    figure('Position', [300, 200, 800, 400], 'Name', '剖面对比');
    
    % 距离向剖面
    subplot(1, 2, 1);
    range_profile_original = abs(ISAR_original(:, round(Ntm/2)));
    range_profile_enhanced = abs(ISAR_enhanced(:, round(Ntm/2)));
    
    plot(range_profile_original, 'b-', 'LineWidth', 1.5, 'DisplayName', '原始FFT');
    hold on;
    plot(range_profile_enhanced, 'r-', 'LineWidth', 1.5, 'DisplayName', '增强STVMD');
    xlabel('距离单元');
    ylabel('幅度');
    title('距离向剖面对比');
    legend('show');
    grid on;
    
    % 方位向剖面
    subplot(1, 2, 2);
    azimuth_profile_original = abs(ISAR_original(round(Nr/2), :));
    azimuth_profile_enhanced = abs(ISAR_enhanced(round(Nr/2), :));
    
    plot(azimuth_profile_original, 'b-', 'LineWidth', 1.5, 'DisplayName', '原始FFT');
    hold on;
    plot(azimuth_profile_enhanced, 'r-', 'LineWidth', 1.5, 'DisplayName', '增强STVMD');
    xlabel('方位单元');
    ylabel('幅度');
    title('方位向剖面对比');
    legend('show');
    grid on;
    
    fprintf('   图表生成完成\n');
    
else
    fprintf('\n处理失败，请检查算法实现\n');
end

fprintf('\n=== 快速测试完成 ===\n');

%% 辅助函数
function entropy = calculate_image_entropy(image)
    image_norm = image / sum(image(:));
    entropy = -sum(image_norm(:) .* log2(image_norm(:) + eps));
end

function contrast = calculate_image_contrast(image)
    contrast = std(image(:)) / mean(image(:));
end
