%-------------------------------------------------------------------------%
%--------   混合DCFT-STVMD ISAR成像处理器  -------%
%--------   专为处理三维旋转目标设计，结合DCFT和STVMD优势 -------%
%-------------------------------------------------------------------------%
function [ISAR_image, processing_info] = Hybrid_DCFT_STVMD_ISAR(echo_data, params)
% Hybrid_DCFT_STVMD_ISAR - 基于DCFT和STVMD融合的ISAR成像处理器
%
% 输入:
%   echo_data - 距离压缩后的回波数据，矩阵大小为 [距离单元数 x 方位采样点数]
%   params - 处理参数结构体
%
% 输出:
%   ISAR_image - 融合ISAR成像结果，单位为dB
%   processing_info - 处理信息结构体

% 1. 参数检查和默认值设置
if nargin < 2
    params = struct();
end

% 获取数据尺寸
[num_range_bins, num_azimuth] = size(echo_data);
fprintf('数据尺寸: %d x %d (距离单元 x 方位单元)\n', num_range_bins, num_azimuth);

% 设置默认参数
params = set_default_parameters(params, num_range_bins, num_azimuth, echo_data);

% 2. 数据预处理
fprintf('进行数据预处理...\n');
processed_data = preprocess_data(echo_data, params);

% 3. 数据类型自动判断（仿真/实测）
if strcmp(params.data_type, 'auto')
    params.data_type = detect_data_type(processed_data, params);
    fprintf('自动检测数据类型: %s\n', params.data_type);
end

% 4. 处理模式选择
if strcmp(params.mode, 'auto')
    params.mode = select_processing_mode(processed_data, params);
    fprintf('自动选择处理模式: %s\n', params.mode);
end

% 5. 根据不同模式进行处理
tic;
switch params.mode
    case 'dcft'
        % 仅使用DCFT处理
        fprintf('使用DCFT模式处理...\n');
        [ISAR_image, dcft_info] = process_with_dcft(processed_data, params);
        processing_info = dcft_info;
        processing_info.mode = 'dcft';
        
    case 'stvmd'
        % 仅使用STVMD处理
        fprintf('使用STVMD模式处理...\n');
        [ISAR_image, stvmd_info] = process_with_stvmd(processed_data, params);
        processing_info = stvmd_info;
        processing_info.mode = 'stvmd';
        
    case 'sequential'
        % 序贯处理：先DCFT后STVMD
        fprintf('使用序贯处理模式 (DCFT→STVMD)...\n');
        [dcft_result, dcft_info] = process_with_dcft(processed_data, params);
        [ISAR_image, stvmd_info] = process_with_stvmd(dcft_info.compensated_data, params);
        
        % 合并处理信息
        processing_info = struct();
        processing_info.dcft = dcft_info;
        processing_info.stvmd = stvmd_info;
        processing_info.mode = 'sequential';
        
    case 'hybrid'
        % 混合处理：参数交互的融合模式
        fprintf('使用混合处理模式...\n');
        [ISAR_image, hybrid_info] = process_with_hybrid(processed_data, params);
        processing_info = hybrid_info;
        processing_info.mode = 'hybrid';
        
    otherwise
        error('未知的处理模式: %s', params.mode);
end
processing_time = toc;
fprintf('总处理时间: %.3f 秒\n', processing_time);

% 6. 图像显示
if params.display
    % 显示成像结果
    figure('Name', sprintf('混合DCFT-STVMD ISAR成像结果 (%s模式)', params.mode));
    imagesc(ISAR_image);
    caxis([-params.processing.dynamic_range_db, 0]);
    colormap(jet); colorbar;
    title(sprintf('ISAR成像结果 (%s模式)', params.mode));
    xlabel('方位单元');
    ylabel('距离单元');
    axis xy; grid on;
end

% 添加处理时间到输出信息
processing_info.time = processing_time;
processing_info.params = params;

end

%% 辅助函数

function params = set_default_parameters(params, num_range_bins, num_azimuth, echo_data)
    % 设置默认处理参数
    
    % 基本参数
    if ~isfield(params, 'mode')
        params.mode = 'auto'; % 'auto', 'dcft', 'stvmd', 'sequential', 'hybrid'
    end
    
    if ~isfield(params, 'data_type')
        params.data_type = 'auto'; % 'auto', 'simulated', 'measured'
    end
    
    if ~isfield(params, 'display')
        params.display = true; % 是否显示结果
    end
    
    % 雷达参数
    if ~isfield(params, 'radar')
        params.radar = struct();
    end
    if ~isfield(params.radar, 'fc')
        params.radar.fc = 5.2e9; % Hz
    end
    if ~isfield(params.radar, 'B')
        params.radar.B = 80e6; % Hz
    end
    if ~isfield(params.radar, 'PRF')
        params.radar.PRF = 1400; % Hz
    end
    
    % 处理参数
    if ~isfield(params, 'processing')
        params.processing = struct();
    end
    if ~isfield(params.processing, 'range_bins')
        params.processing.range_bins = 'auto';
    end
    if ischar(params.processing.range_bins) && strcmp(params.processing.range_bins, 'auto')
        % 自动检测有意义的距离单元
        if exist('echo_data', 'var') && ~isempty(echo_data)
            energy = sum(abs(echo_data).^2, 2);
            threshold = mean(energy) + 3*std(energy);
            valid_bins = find(energy > threshold);
            if isempty(valid_bins)
                valid_bins = 1:num_range_bins;
            end
            params.processing.range_bins = max(1, min(valid_bins)-5):min(num_range_bins, max(valid_bins)+5);
            fprintf('自动选择距离单元范围: %d - %d\n', params.processing.range_bins(1), params.processing.range_bins(end));
        else
            % 如果没有传入数据，使用全部距离单元
            params.processing.range_bins = 1:num_range_bins;
            fprintf('无可用数据进行自动选择，使用全部距离单元\n');
        end
    end
    
    if ~isfield(params.processing, 'apply_window')
        params.processing.apply_window = true;
    end
    if ~isfield(params.processing, 'dynamic_range_db')
        params.processing.dynamic_range_db = 30;
    end
    
    % DCFT参数
    if ~isfield(params, 'dcft')
        params.dcft = struct();
    end
    if ~isfield(params.dcft, 'alpha_step')
        params.dcft.alpha_step = 8;
    end
    if ~isfield(params.dcft, 'alpha_min')
        params.dcft.alpha_min = -16;
    end
    if ~isfield(params.dcft, 'alpha_max')
        params.dcft.alpha_max = 320;
    end
    if ~isfield(params.dcft, 'beta_step')
        params.dcft.beta_step = 100;
    end
    if ~isfield(params.dcft, 'beta_min')
        params.dcft.beta_min = -500;
    end
    if ~isfield(params.dcft, 'beta_max')
        params.dcft.beta_max = 2400;
    end
    if ~isfield(params.dcft, 'thresholding')
        params.dcft.thresholding = true;
    end
    if ~isfield(params.dcft, 'threshold_ratio')
        params.dcft.threshold_ratio = 0.2;
    end
    
    % STVMD参数
    if ~isfield(params, 'stvmd')
        params.stvmd = struct();
    end
    if ~isfield(params.stvmd, 'K')
        params.stvmd.K = 3; % 模态数量
    end
    if ~isfield(params.stvmd, 'alpha')
        params.stvmd.alpha = 2000; % 平衡参数
    end
    if ~isfield(params.stvmd, 'tau')
        params.stvmd.tau = 0.1; % 拉格朗日乘子更新步长
    end
    if ~isfield(params.stvmd, 'tol')
        params.stvmd.tol = 1e-7; % 收敛容限
    end
    if ~isfield(params.stvmd, 'window_sizes')
        params.stvmd.window_sizes = [16, 32, 64]; % 多尺度窗口大小
    end
    if ~isfield(params.stvmd, 'overlap')
        params.stvmd.overlap = 0.5; % 窗口重叠率
    end
    if ~isfield(params.stvmd, 'dynamic')
        params.stvmd.dynamic = true; % 是否使用动态中心频率
    end
    if ~isfield(params.stvmd, 'max_iter')
        params.stvmd.max_iter = 500; % 最大迭代次数
    end
    if ~isfield(params.stvmd, 'global_iterations')
        params.stvmd.global_iterations = 3; % 全局迭代次数
    end
    
    % 融合参数
    if ~isfield(params, 'fusion')
        params.fusion = struct();
    end
    if ~isfield(params.fusion, 'method')
        params.fusion.method = 'sequential'; % 'sequential', 'weighted'
    end
    if ~isfield(params.fusion, 'weight_dcft')
        params.fusion.weight_dcft = 0.5;
    end
    if ~isfield(params.fusion, 'weight_stvmd')
        params.fusion.weight_stvmd = 0.5;
    end
end

function processed_data = preprocess_data(echo_data, params)
    % 数据预处理
    [num_range_bins, num_azimuth] = size(echo_data);
    processed_data = echo_data;
    
    % 窗函数处理
    if params.processing.apply_window
        % 应用汉明窗进行旁瓣抑制
        window = hamming(num_azimuth)';
        window_matrix = repmat(window, num_range_bins, 1);
        processed_data = processed_data .* window_matrix;
    end
end

function data_type = detect_data_type(data, params)
    % 自动判断数据类型（仿真/实测）
    % 基于数据特性判断
    
    % 提取相位特性
    phase_variation = std(diff(unwrap(angle(data(params.processing.range_bins,:)),[],2),[],2));
    mean_phase_var = mean(phase_variation);
    
    % 提取幅度特性
    amplitude = abs(data);
    amplitude_variation = std(amplitude(params.processing.range_bins,:),[],2) ./ mean(amplitude(params.processing.range_bins,:),2);
    mean_amp_var = mean(amplitude_variation);
    
    % 根据相位变化和幅度特性判断
    if mean_phase_var < 0.8 && mean_amp_var < 0.5
        data_type = 'simulated'; % 相位变化较小、幅度较稳定，可能是仿真数据
    else
        data_type = 'measured'; % 相位变化较大、幅度波动较大，可能是实测数据
    end
end

function mode = select_processing_mode(data, params)
    % 自动选择处理模式
    
    if strcmp(params.data_type, 'simulated')
        % 仿真数据优先使用DCFT
        mode = 'dcft';
    else
        % 实测数据优先使用序贯处理
        mode = 'sequential';
    end
    
    % 更复杂的自适应选择逻辑可以在这里添加
end

function [result, info] = process_with_dcft(data, params)
    % 使用DCFT进行处理
    fprintf('开始DCFT处理...\n');
    
    % 选择目标的距离单元范围
    range_bins = params.processing.range_bins;
    [num_range_bins, num_azimuth] = size(data);
    
    % 准备DCFT参数
    alpha_values = params.dcft.alpha_min:params.dcft.alpha_step:params.dcft.alpha_max;
    beta_values = params.dcft.beta_min:params.dcft.beta_step:params.dcft.beta_max;
    num_alpha = length(alpha_values);
    num_beta = length(beta_values);
    
    % 生成时间向量
    tm = (0:num_azimuth-1) / params.radar.PRF;
    
    % 初始化DCFT结果矩阵和补偿后的数据
    DCFT_result = zeros(num_range_bins, num_azimuth);
    compensated_data = zeros(size(data));
    best_params = zeros(length(range_bins), 3); % [alpha, beta, max_response]
    
    % 对选定距离单元进行DCFT处理
    for idx = 1:length(range_bins)
        r_idx = range_bins(idx);
        
        % 显示进度
        if mod(idx, 5) == 0 || idx == 1 || idx == length(range_bins)
            fprintf('处理距离单元 %d/%d (%.1f%%)\n', idx, length(range_bins), 100*idx/length(range_bins));
        end
        
        % 提取当前距离单元的信号
        signal = data(r_idx, :);
        
        % 初始化参数
        max_response = 0;
        best_alpha = 0;
        best_beta = 0;
        best_spectrum = zeros(1, num_azimuth);
        
        % 参数搜索
        for a_idx = 1:num_alpha
            alpha = alpha_values(a_idx);
            
            for b_idx = 1:num_beta
                beta = beta_values(b_idx);
                
                % 生成补偿信号
                compensation = exp(-1j * 2*pi * ((1/2)*alpha*tm.*tm + (1/6)*beta*tm.*tm.*tm));
                
                % 应用去啁啾
                dechirped = signal .* compensation;
                
                % FFT处理
                spectrum = fft(dechirped);
                
                % 检查是否有更好的响应
                current_max = max(abs(spectrum));
                if current_max > max_response
                    max_response = current_max;
                    best_alpha = alpha;
                    best_beta = beta;
                    best_spectrum = spectrum;
                end
            end
        end
        
        % 存储最佳参数和结果
        best_params(idx, :) = [best_alpha, best_beta, max_response];
        DCFT_result(r_idx, :) = abs(best_spectrum);
        
        % 存储补偿后的数据
        best_compensation = exp(-1j * 2*pi * ((1/2)*best_alpha*tm.*tm + (1/6)*best_beta*tm.*tm.*tm));
        compensated_data(r_idx, :) = signal .* best_compensation;
    end
    
    % 阈值处理
    if params.dcft.thresholding
        for r_idx = range_bins
            bin_max = max(DCFT_result(r_idx, :));
            threshold = bin_max * params.dcft.threshold_ratio;
            DCFT_result(r_idx, DCFT_result(r_idx, :) < threshold) = 0;
        end
    end
    
    % 生成最终图像
    result = 20*log10(abs(DCFT_result)./max(abs(DCFT_result(:))));
    
    % 返回处理信息
    info = struct();
    info.best_params = best_params;
    info.compensated_data = compensated_data;
    
    % 计算图像质量
    info.contrast = calculate_contrast(DCFT_result);
    info.entropy = calculate_entropy(abs(DCFT_result));
    
    fprintf('DCFT处理完成，对比度: %.4f, 熵: %.4f\n', info.contrast, info.entropy);
end

function contrast = calculate_contrast(image)
    % 计算图像对比度
    magnitude = abs(image);
    contrast = std(magnitude(:)) / mean(magnitude(:));
end

function entropy = calculate_entropy(image)
    % 计算图像熵
    normalized = image / sum(image(:));
    entropy = -sum(normalized(:) .* log2(normalized(:) + eps));
end

function [result, info] = process_with_stvmd(data, params)
    % 使用STVMD进行处理
    fprintf('开始STVMD处理...\n');
    
    % 选择目标的距离单元范围
    range_bins = params.processing.range_bins;
    [num_range_bins, num_azimuth] = size(data);
    
    % 生成时间向量
    tm = (0:num_azimuth-1) / params.radar.PRF;
    
    % 初始化结果
    stvmd_result = zeros(num_range_bins, num_azimuth);
    compensated_data = zeros(size(data));
    
    % STVMD参数
    K = params.stvmd.K;                 % 模态数量
    alpha = params.stvmd.alpha;         % 平衡参数
    tau = params.stvmd.tau;             % 拉格朗日乘子更新步长
    tol = params.stvmd.tol;             % 收敛容限
    window_sizes = params.stvmd.window_sizes;  % 窗口大小数组
    overlap = params.stvmd.overlap;     % 窗口重叠率
    max_iter = params.stvmd.max_iter;   % 最大迭代次数
    
    % 对每个距离单元进行处理
    for idx = 1:length(range_bins)
        r_idx = range_bins(idx);
        
        % 显示进度
        if mod(idx, 5) == 0 || idx == 1 || idx == length(range_bins)
            fprintf('处理距离单元 %d/%d (%.1f%%)\n', idx, length(range_bins), 100*idx/length(range_bins));
        end
        
        % 提取当前距离单元的信号
        signal = data(r_idx, :);
        
        % 多尺度STVMD处理
        decomp_results = cell(length(window_sizes), 1);
        decomp_qualities = zeros(length(window_sizes), 1);
        
        for w_idx = 1:length(window_sizes)
            window_size = window_sizes(w_idx);
            
            % 短时处理
            [decomp, quality] = stvmd_decomposition(signal, K, window_size, overlap, alpha, tau, tol, max_iter, params.stvmd.dynamic);
            decomp_results{w_idx} = decomp;
            decomp_qualities(w_idx) = quality;
        end
        
        % 基于质量指标选择最佳分解或融合多尺度结果
        [~, best_idx] = max(decomp_qualities);
        best_decomp = decomp_results{best_idx};
        
        % 选择参考模态（通常是频率最稳定的模态）
        ref_mode_idx = select_reference_mode(best_decomp, K);
        
        % 提取相位误差并补偿
        phase_error = estimate_phase_error(signal, best_decomp, ref_mode_idx);
        compensated_signal = signal .* exp(-1j * phase_error);
        
        % 保存结果
        stvmd_result(r_idx, :) = abs(fft(compensated_signal));
        compensated_data(r_idx, :) = compensated_signal;
    end
    
    % 生成最终图像
    result = 20*log10(abs(stvmd_result)./max(abs(stvmd_result(:))));
    
    % 返回处理信息
    info = struct();
    info.compensated_data = compensated_data;
    
    % 计算图像质量
    info.contrast = calculate_contrast(stvmd_result);
    info.entropy = calculate_entropy(abs(stvmd_result));
    
    fprintf('STVMD处理完成，对比度: %.4f, 熵: %.4f\n', info.contrast, info.entropy);
end

function [modes, quality] = stvmd_decomposition(signal, K, window_size, overlap, alpha, tau, tol, max_iter, dynamic)
    % 短时变分模态分解
    N = length(signal);
    
    % 计算窗口参数
    step = round(window_size * (1 - overlap));
    num_windows = floor((N - window_size) / step) + 1;
    
    % 初始化模态和中心频率
    modes = zeros(K, N);
    omega = zeros(K, num_windows);
    
    % 对每个窗口进行处理
    for win_idx = 1:num_windows
        % 计算窗口范围
        start_idx = (win_idx - 1) * step + 1;
        end_idx = start_idx + window_size - 1;
        
        % 提取窗口数据
        window_data = signal(start_idx:end_idx);
        
        % 对窗口应用汉明窗
        window_func = hamming(window_size);
        windowed_data = window_data .* window_func';
        
        % 初始化窗口内的模态和拉格朗日乘子
        u_hat = zeros(K, window_size);
        omega_hat = zeros(K, 1);
        lambda_hat = zeros(1, window_size);
        
        % 初始化中心频率
        if win_idx == 1 || ~dynamic
            % 均匀分布初始频率
            for k = 1:K
                omega_hat(k) = (k - 1) / K * 0.5;
            end
        else
            % 使用前一个窗口的结果初始化
            omega_hat = omega(:, win_idx-1);
        end
        
        % VMD迭代
        for iter = 1:max_iter
            % 保存旧的中心频率
            old_omega = omega_hat;
            
            % 更新每个模态
            for k = 1:K
                % 计算残差
                sum_uk = zeros(1, window_size);
                for k_prime = 1:K
                    if k_prime ~= k
                        sum_uk = sum_uk + u_hat(k_prime, :);
                    end
                end
                residual = windowed_data - sum_uk;
                
                % 更新模态
                u_hat(k, :) = (residual + lambda_hat/2) ./ (1 + alpha * (fftshift(((0:window_size-1) - omega_hat(k) * window_size).^2)));
                
                % 更新中心频率
                omega_hat(k) = sum(abs(fftshift(fft(u_hat(k, :)))).^2 .* (0:window_size-1)) / ...
                               sum(abs(fftshift(fft(u_hat(k, :)))).^2) / window_size;
            end
            
            % 更新拉格朗日乘子
            sum_uk = sum(u_hat, 1);
            lambda_hat = lambda_hat + tau * (sum_uk - windowed_data);
            
            % 检查收敛性
            if max(abs(omega_hat - old_omega)) < tol
                break;
            end
        end
        
        % 保存中心频率
        omega(:, win_idx) = omega_hat;
        
        % 将窗口结果合并到全局模态
        for k = 1:K
            % 使用重叠-相加方法
            if win_idx == 1
                modes(k, start_idx:end_idx) = u_hat(k, :);
            else
                % 线性加权融合重叠区域
                overlap_start = start_idx;
                overlap_end = start_idx + step - 1;
                
                % 创建线性权重
                weights = linspace(0, 1, step);
                
                % 在重叠区域应用加权
                modes(k, overlap_start:overlap_end) = (1 - weights) .* modes(k, overlap_start:overlap_end) + ...
                                                     weights .* u_hat(k, 1:step);
                
                % 非重叠区域直接赋值
                modes(k, (overlap_end+1):end_idx) = u_hat(k, (step+1):end);
            end
        end
    end
    
    % 计算分解质量
    quality = assess_decomposition_quality(signal, modes);
end

function quality = assess_decomposition_quality(signal, modes)
    % 评估分解质量
    
    % 计算重构信号
    reconstructed = sum(modes, 1);
    
    % 计算重构误差
    error = signal - reconstructed;
    mse = mean(abs(error).^2);
    
    % 计算模态能量集中度
    K = size(modes, 1);
    spectral_concentration = 0;
    
    for k = 1:K
        mode_spectrum = abs(fft(modes(k, :)));
        % 计算频谱能量集中度（作为频谱峰值与平均值的比值）
        spectral_concentration = spectral_concentration + max(mode_spectrum) / mean(mode_spectrum);
    end
    spectral_concentration = spectral_concentration / K;
    
    % 综合质量指标 (低MSE和高能量集中度表示高质量)
    quality = spectral_concentration / (1 + mse);
end

function ref_idx = select_reference_mode(modes, K)
    % 选择参考模态
    
    % 计算每个模态的特性
    stability = zeros(K, 1);
    energy = zeros(K, 1);
    
    for k = 1:K
        % 计算模态的频率稳定性
        mode_spectrum = abs(fft(modes(k, :)));
        [peak_val, ~] = max(mode_spectrum);
        stability(k) = peak_val / sum(mode_spectrum);
        
        % 计算模态能量
        energy(k) = sum(abs(modes(k, :)).^2);
    end
    
    % 归一化
    stability = stability / max(stability);
    energy = energy / max(energy);
    
    % 综合考虑稳定性和能量
    score = stability .* energy;
    [~, ref_idx] = max(score);
end

function phase_error = estimate_phase_error(signal, modes, ref_mode_idx)
    % 估计相位误差
    
    % 获取参考模态
    ref_mode = modes(ref_mode_idx, :);
    
    % 计算信号与参考模态的相位差
    signal_phase = unwrap(angle(signal));
    ref_phase = unwrap(angle(ref_mode));
    
    % 估计相位误差
    phase_error = signal_phase - ref_phase;
    
    % 平滑相位误差
    phase_error = smooth_phase(phase_error, 5);
end

function smoothed_phase = smooth_phase(phase, window_size)
    % 平滑相位
    kernel = ones(1, window_size) / window_size;
    smoothed_phase = conv(phase, kernel, 'same');
end

function [result, info] = process_with_hybrid(data, params)
    % 混合处理模式
    fprintf('开始混合处理...\n');
    
    % 1. 首先使用DCFT进行粗略补偿
    [~, dcft_info] = process_with_dcft(data, params);
    compensated_data = dcft_info.compensated_data;
    
    % 2. 使用STVMD进行精细相位误差估计
    % 选择目标的距离单元范围
    range_bins = params.processing.range_bins;
    [num_range_bins, num_azimuth] = size(data);
    
    % 初始化结果
    hybrid_result = zeros(num_range_bins, num_azimuth);
    
    % 对每个距离单元进行处理
    for idx = 1:length(range_bins)
        r_idx = range_bins(idx);
        
        if mod(idx, 5) == 0 || idx == 1 || idx == length(range_bins)
            fprintf('混合处理距离单元 %d/%d (%.1f%%)\n', idx, length(range_bins), 100*idx/length(range_bins));
        end
        
        % 提取DCFT补偿后的信号
        signal = compensated_data(r_idx, :);
        
        % DCFT参数提供STVMD的初始化信息
        dcft_alpha = dcft_info.best_params(idx, 1);
        dcft_beta = dcft_info.best_params(idx, 2);
        
        % 根据DCFT结果修改STVMD参数
        stvmd_params = params.stvmd;
        
        % 自适应调整模态数量
        if abs(dcft_alpha) > 100 || abs(dcft_beta) > 1000
            stvmd_params.K = min(5, params.stvmd.K + 1); % 复杂运动需要更多模态
        else
            stvmd_params.K = params.stvmd.K;
        end
        
        % 自适应调整窗口大小
        signal_complexity = assess_signal_complexity(signal);
        if signal_complexity > 0.7
            % 复杂信号使用较小窗口
            stvmd_params.window_sizes = max(8, params.stvmd.window_sizes - 8);
        end
        
        % 执行单距离单元的STVMD分解
        [modes, ~] = stvmd_decomposition(signal, stvmd_params.K, stvmd_params.window_sizes(1), ...
                                         stvmd_params.overlap, stvmd_params.alpha, ...
                                         stvmd_params.tau, stvmd_params.tol, ...
                                         stvmd_params.max_iter, stvmd_params.dynamic);
        
        % 选择参考模态
        ref_mode_idx = select_reference_mode(modes, stvmd_params.K);
        
        % 估计残余相位误差
        phase_error = estimate_phase_error(signal, modes, ref_mode_idx);
        
        % 应用精细相位补偿
        final_signal = signal .* exp(-1j * phase_error);
        
        % 保存结果
        hybrid_result(r_idx, :) = abs(fft(final_signal));
    end
    
    % 生成最终图像
    result = 20*log10(abs(hybrid_result)./max(abs(hybrid_result(:))));
    
    % 返回处理信息
    info = struct();
    info.dcft_info = dcft_info;
    
    % 计算图像质量
    info.contrast = calculate_contrast(hybrid_result);
    info.entropy = calculate_entropy(abs(hybrid_result));
    
    fprintf('混合处理完成，对比度: %.4f, 熵: %.4f\n', info.contrast, info.entropy);
end

function complexity = assess_signal_complexity(signal)
    % 评估信号复杂度
    
    % 计算相位变化率
    phase = unwrap(angle(signal));
    phase_diff = diff(phase);
    
    % 计算相位变化的标准差
    std_phase = std(phase_diff);
    
    % 计算频谱熵
    spectrum = abs(fft(signal));
    spectrum = spectrum / sum(spectrum);
    entropy = -sum(spectrum .* log2(spectrum + eps));
    
    % 归一化
    max_entropy = log2(length(signal));
    norm_entropy = entropy / max_entropy;
    
    % 综合评估
    complexity = (0.5 * std_phase / pi + 0.5 * norm_entropy);
    
    % 确保范围在[0,1]
    complexity = min(1, max(0, complexity));
end 