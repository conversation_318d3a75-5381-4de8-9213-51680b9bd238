%-------------------------------------------------------------------------%
%--------   混合DCFT-STVMD ISAR成像处理器示例脚本  -------%
%--------   自动加载数据并执行处理                       -------%
%-------------------------------------------------------------------------%

clc;
clear all;
close all;

fprintf('混合DCFT-STVMD ISAR处理器演示\n');
fprintf('====================================\n\n');

% 1. 尝试加载数据
data_loaded = false;
data_files = {'shipx2.mat', 'data_ship.mat', 'data_ship2.mat', 'data_ship3.mat'};

for i = 1:length(data_files)
    try
        fprintf('尝试加载数据文件: %s\n', data_files{i});
        data = load(data_files{i});
        data_fields = fieldnames(data);
        
        % 检查数据字段
        for j = 1:length(data_fields)
            field = data_fields{j};
            field_data = data.(field);
            
            % 检查是否为有效的回波数据矩阵
            if isnumeric(field_data) && ~isscalar(field_data) && ~isvector(field_data) && ismatrix(field_data)
                % 找到可能的回波数据
                echo_data = field_data;
                fprintf('成功加载回波数据，大小: %d x %d\n', size(echo_data, 1), size(echo_data, 2));
                data_loaded = true;
                break;
            end
        end
        
        if data_loaded
            break;
        end
    catch
        fprintf('无法加载数据文件: %s\n', data_files{i});
    end
end

% 如果未找到数据，尝试创建简单的仿真数据
if ~data_loaded
    fprintf('未找到有效数据文件，使用简单仿真数据...\n');
    
    % 创建简单的仿真数据
    num_range_bins = 100;
    num_azimuth = 700;
    
    % 创建时间向量
    t = (0:num_azimuth-1) / 1400; % PRF = 1400 Hz
    
    % 创建几个散射点
    echo_data = zeros(num_range_bins, num_azimuth);
    
    % 添加几个带有不同运动特性的散射点
    scatter_points = [30, 40, 50, 60];
    for i = 1:length(scatter_points)
        r_idx = scatter_points(i);
        
        % 不同的频率和相位参数
        f0 = 190 + 10*i;
        alpha = 40 + 5*i;
        beta = 400 + 50*i;
        
        % 创建带有多项式相位的信号
        phase = 2*pi * (f0*t + (1/2)*alpha*t.^2 + (1/6)*beta*t.^3);
        echo_data(r_idx, :) = exp(1j * phase);
    end
    
    % 添加噪声
    noise = 0.1 * (randn(size(echo_data)) + 1j*randn(size(echo_data)));
    echo_data = echo_data + noise;
    
    fprintf('已创建仿真数据，大小: %d x %d\n', size(echo_data, 1), size(echo_data, 2));
    data_loaded = true;
end

% 2. 设置处理参数
fprintf('\n配置处理参数...\n');

params = struct();

% 基本参数
params.mode = 'auto';            % 处理模式：自动选择
params.data_type = 'auto';       % 数据类型：自动检测
params.display = true;           % 显示结果

% 雷达参数
params.radar.fc = 5.2e9;         % 载频 (Hz)
params.radar.B = 80e6;           % 带宽 (Hz)
params.radar.PRF = 1400;         % 脉冲重复频率 (Hz)

% 处理参数
params.processing.range_bins = 'auto';  % 自动检测距离单元
params.processing.apply_window = true;  % 应用窗函数
params.processing.dynamic_range_db = 30;  % 显示动态范围 (dB)

% DCFT参数
params.dcft.alpha_step = 8;      % 啁啾率搜索步长
params.dcft.alpha_min = -16;     % 最小啁啾率
params.dcft.alpha_max = 320;     % 最大啁啾率
params.dcft.beta_step = 100;     % 啁啾率导数搜索步长
params.dcft.beta_min = -500;     % 最小啁啾率导数
params.dcft.beta_max = 2400;     % 最大啁啾率导数
params.dcft.thresholding = true; % 启用阈值处理
params.dcft.threshold_ratio = 0.2; % 阈值比例

% STVMD参数
params.stvmd.K = 3;              % 模态数量
params.stvmd.alpha = 2000;       % 平衡参数
params.stvmd.tau = 0.1;          % 拉格朗日乘子更新步长
params.stvmd.tol = 1e-7;         % 收敛容限
params.stvmd.window_sizes = [16, 32, 64]; % 多尺度窗口大小
params.stvmd.overlap = 0.5;      % 窗口重叠率
params.stvmd.dynamic = true;     % 是否使用动态中心频率
params.stvmd.max_iter = 500;     % 最大迭代次数
params.stvmd.global_iterations = 3; % 全局迭代次数

% 融合参数
params.fusion.method = 'sequential'; % 融合方法
params.fusion.weight_dcft = 0.5;  % DCFT权重
params.fusion.weight_stvmd = 0.5; % STVMD权重

% 3. 运行处理器
fprintf('\n开始执行混合DCFT-STVMD ISAR处理...\n');
fprintf('====================================\n\n');

tic;
[ISAR_image, processing_info] = Hybrid_DCFT_STVMD_ISAR(echo_data, params);
processing_time = toc;

% 4. 显示性能比较
fprintf('\n处理性能比较：\n');
fprintf('====================================\n');

% 比较不同模式
modes = {'dcft', 'stvmd', 'sequential', 'hybrid'};
mode_names = {'DCFT', 'STVMD', '序贯处理', '混合处理'};

% 当前使用的模式
current_mode = processing_info.mode;
fprintf('当前使用的处理模式: %s\n\n', current_mode);

% 输出图像质量指标
fprintf('图像质量指标：\n');
fprintf('- 对比度: %.4f\n', processing_info.contrast);
fprintf('- 熵: %.4f\n', processing_info.entropy);
fprintf('- 处理时间: %.3f 秒\n', processing_time);

% 5. 保存结果
try
    save('DCFT_STVMD_ISAR_Results.mat', 'ISAR_image', 'processing_info', 'params');
    fprintf('\n结果已保存到 DCFT_STVMD_ISAR_Results.mat\n');
catch
    fprintf('\n保存结果时出错\n');
end

fprintf('\n处理完成！\n'); 