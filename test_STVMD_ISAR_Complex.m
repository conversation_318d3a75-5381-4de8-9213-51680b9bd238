% test_STVMD_ISAR_Complex.m
% 测试STVMD算法在复杂运动ISAR成像中的应用

clc; clear all; close all;
%% 1. 生成或加载ISAR数据
fprintf('=== STVMD-ISAR复杂运动成像测试 ===\n');
fprintf('1. 加载ISAR数据...\n');

% 尝试加载船只数据
try
    load shipx2_1000.mat;
    fprintf('   成功加载船只数据 shipx2.mat\n');
    use_simulation = false;
catch
    fprintf('   未找到数据文件，将使用模拟数据...\n');
    use_simulation = true;
end

if use_simulation
    fprintf('2. 生成ISAR仿真数据...\n');
    % 使用ISARrot_trans.m中的散射点模型
    Pos = [-10 -1 0;-9 -1 0;-8 -1 0;-7 -1 0;-6 -1 0;-5 -1 0;-3.75 -1 0;-3 -1 0;-2 -1 0;-1 -1 0;...
           0 -1 0;...
           1 -1 0;2 -1 0;3 -1 0;4 -1 0;5 -1 0;6 -1 0;7 -1 0;8 -1 0;9 -1 0;10 -1 0;...
           -9.5 0.2 0.5;...
           -9.5 1.2 0.5;-9 1 0;-8 1 0;-7 1 0;-6 1 0;-5.2 1.2 0;-4.1 1 0;-3 1 0;-2 1 0;-1 1 0;...
           0 1 0;...
           1 1 0;2 1 0;3 1 0;4 1 0;5 1 0;6 1 0;7 1 0;8 1 0;9 1 0;10 1 0;...
           10.5 -0.75 0;10.5 0.75 0;11 -0.5 0;11 0.5 0;11.5 0 0;...
           9.5 0.5 0.5;9.5 -0.5 0.5;9 0 0.5;8.5 0.5 0.5;8.5 -0.5 0.5;...
           5 0 0.5;5 0 1;5 0 1.5;5 0 2;5 0 2.5;5 0 3;5 0 3.5;5 0 4;...
           5.5 0.5 0.5;5.5 -0.5 0.5;4.5 0.5 0.5;4.5 -0.5 0.5;...
           0.5 0.5 0.9;0.5 -0.5 0.9;-0.5 0.5 0.9;-0.5 -0.5 0.9;0 0 0.5;...
           -5 0 0.2;-5 0.2 0.8;-5 0.2 1.4;-5 0 2;...
           -5.5 -0.5 0.5;-5.5 0.5 0.5;-4.4 0.5 0.5;-4.5 -0.6 0.5];

    % 坐标缩放
    x_Pos = Pos(:,1)*5;
    y_Pos = Pos(:,2)*5;
    z_Pos = Pos(:,3)*5;

    % 雷达参数
    B = 80*1e6;          % 带宽
    c = 3*1e8;           % 光速
    PRF = 1400;          % 脉冲重复频率
    fc = 5.2*1e9;        % 载频
    delta_r = c/(2*B);   % 距离分辨率

    % 生成距离和时间轴
    r = -50*delta_r:delta_r:50*delta_r;
    tm = 0:(1/PRF):0.501;
    Nr = length(r);
    Ntm = length(tm);

    % 雷达视线单位矢量
    R = [cos(3*pi/8)*cos(0), cos(3*pi/8)*sin(0), sin(3*pi/8)];

    % 计算运动参数
    Num_point = length(x_Pos);
    x_r = zeros(1,Num_point);
    y_r = zeros(1,Num_point);
    z_r = zeros(1,Num_point);

    for n_point = 1:Num_point
        x_r(n_point) = y_Pos(n_point)*R(3) - z_Pos(n_point)*R(2);
        y_r(n_point) = z_Pos(n_point)*R(1) - x_Pos(n_point)*R(3);
        z_r(n_point) = x_Pos(n_point)*R(2) - y_Pos(n_point)*R(1);
    end

    % 复杂运动参数（包含立方项）
    x_omega = 0.05;  % 初始角速度
    y_omega = 0.2;
    z_omega = 0.05;
    x_alpha = 0.05;  % 角加速度
    y_alpha = 0.1;
    z_alpha = 0.05;
    x_gamma = 0.05;  % 角加加速度（立方项）
    y_gamma = 0.4;
    z_gamma = 0.05;

    % 计算每个散射点的运动参数
    f_v = zeros(1,Num_point);     % 速度项
    alpha_a = zeros(1,Num_point); % 加速度项
    beta_j = zeros(1,Num_point);  % 加加速度项（立方）

    for n_point = 1:Num_point
        f_v(n_point) = x_r(n_point)*x_omega + y_r(n_point)*y_omega + z_r(n_point)*z_omega;
        alpha_a(n_point) = x_r(n_point)*x_alpha + y_r(n_point)*y_alpha + z_r(n_point)*z_alpha;
        beta_j(n_point) = x_r(n_point)*x_gamma + y_r(n_point)*y_gamma + z_r(n_point)*z_gamma;
    end

    % 生成ISAR回波信号
    fprintf('3. 生成包含立方相位的ISAR回波...\n');
    s_r_tm = zeros(Nr, Ntm, 'like', 1j);
    ones_r = ones(1, Nr);
    ones_tm = ones(1, Ntm);

    for n_point = 1:Num_point
        % 初始距离
        Delta_R0 = x_Pos(n_point)*R(1) + y_Pos(n_point)*R(2) + z_Pos(n_point)*R(3);

        % 瞬时距离（包含立方项）
        Delta_R_t = Delta_R0 + f_v(n_point).*tm + (1/2)*alpha_a(n_point).*tm.^2 + (1/6)*beta_j(n_point).*tm.^3;

        % 相位
        phase_t = (4*pi*fc/c) * Delta_R_t;

        % 幅度调制
        amplitude_factor = 1;
        if n_point > 53 && n_point < 62
            amplitude_factor = 1.3;
        end
        if n_point == 48
            amplitude_factor = amplitude_factor * 1;
        end

        % 累加回波
        s_r_tm = s_r_tm + amplitude_factor * sinc((2*B/c)*(r.'*ones_tm - ones_r.'*Delta_R_t)) .* exp(1j*ones_r.'*phase_t);
    end

    % 添加噪声
    SNR_dB = 20;
    signal_power = mean(abs(s_r_tm(:)).^2);
    noise_power = signal_power / (10^(SNR_dB/10));
    noise = sqrt(noise_power/2) * (randn(Nr, Ntm) + 1j*randn(Nr, Ntm));
    s_r_tm_noisy = s_r_tm + noise;

    fprintf('4. 数据生成完成，SNR = %d dB\n', SNR_dB);
else
    % 使用加载的数据
    s_r_tm_noisy = shipx2_1000;
    [Nr, Ntm] = size(s_r_tm_noisy);
    fprintf('   使用实测数据，大小: %d x %d\n', Nr, Ntm);
end

%% 2. 原始FFT处理
% 直接FFT处理
ISAR_original = fftshift(fft(s_r_tm_noisy, [], 2), 2);
ISAR_original_db = 20*log10(abs(ISAR_original)/max(abs(ISAR_original(:))));

% 显示原始FFT结果
figure('Name', '原始FFT处理结果');
imagesc(ISAR_original_db);
caxis([-30, 0]);
colorbar;
xlabel('方位单元');
ylabel('距离单元');
title('原始ISAR图像 (FFT处理)');
colormap('jet');
axis xy;

%% 3. 设置增强STVMD参数
fprintf('\n5. 增强STVMD参数设置:\n');

% 基本参数 - 根据数据类型调整为最优值
params = struct();

if ~use_simulation
    % 针对实测船只数据的优化参数
    params.K = 3;                    % 船只数据模式数量
    params.alpha = 2000;             % 适中的平衡参数
    params.tau = 0.1;                % 双上升时间步长
    params.tol = 1e-7;               % 收敛容限
    params.max_iter = 300;           % 迭代次数
    params.window_length = 64;       % 船只数据推荐窗口长度
    params.overlap = 0.75;           % 重叠率
    fprintf('   使用船只数据优化参数\n');
else
    % 针对复杂运动仿真数据的优化参数
    params.K = 5;                    % 增加模式数量到5个，提高复杂运动捕捉能力
    params.alpha = 5000;             % 增大平衡参数，强化带宽约束
    params.tau = 0.1;                % 双上升时间步长
    params.tol = 1e-8;               % 提高收敛容限
    params.max_iter = 500;           % 增加最大迭代次数以确保收敛
    params.window_length = 128;      % 增大STFT窗口长度，提高频率分辨率
    params.overlap = 0.85;           % 增大重叠率，提高平滑性
    fprintf('   使用仿真数据优化参数\n');
end

% 新增的增强功能参数
params.use_adaptive_params = true;           % 启用自适应参数调整
params.use_enhanced_phase_estimation = true; % 启用增强相位估计
params.use_sidelobe_suppression = true;      % 启用旁瓣抑制
params.sidelobe_method = 'adaptive';         % 旁瓣抑制方法: 'apodization', 'clean', 'adaptive'
params.use_keystone = false;                 % Keystone变换（可选）
params.entropy_threshold = 0.1;              % 熵阈值

fprintf('   - 模式数量 K = %d\n', params.K);
fprintf('   - 平衡参数 alpha = %d\n', params.alpha);
fprintf('   - STFT窗口长度 = %d\n', params.window_length);
fprintf('   - 窗口重叠率 = %.2f\n', params.overlap);
fprintf('   - 自适应参数调整 = %s\n', string(params.use_adaptive_params));
fprintf('   - 增强相位估计 = %s\n', string(params.use_enhanced_phase_estimation));
fprintf('   - 旁瓣抑制 = %s (%s)\n', string(params.use_sidelobe_suppression), params.sidelobe_method);
fprintf('   - Keystone校正 = %s\n', string(params.use_keystone));

%% 4. 执行增强STVMD-ISAR处理
fprintf('\n6. 开始增强STVMD-ISAR处理...\n');
tic;
[ISAR_image, decomposed_modes, phase_errors, analysis_results] = STVMD_ISAR_Complex_Motion(s_r_tm_noisy, params);
processing_time = toc;
fprintf('   处理完成，耗时: %.2f 秒\n', processing_time);

%% 5. 增强性能评估
% 计算图像熵
entropy_original = calculate_image_entropy(abs(ISAR_original));
entropy_stvmd = calculate_image_entropy(abs(ISAR_image));

% 计算图像对比度
contrast_original = calculate_image_contrast(abs(ISAR_original));
contrast_stvmd = calculate_image_contrast(abs(ISAR_image));

% 计算峰值旁瓣比 (PSLR)
pslr_original = calculate_pslr(abs(ISAR_original));
pslr_stvmd = calculate_pslr(abs(ISAR_image));

% 计算积分旁瓣比 (ISLR)
islr_original = calculate_islr(abs(ISAR_original));
islr_stvmd = calculate_islr(abs(ISAR_image));

fprintf('\n7. 增强性能评估:\n');
fprintf('   - 原始图像熵: %.4f\n', entropy_original);
fprintf('   - 增强STVMD处理后图像熵: %.4f\n', entropy_stvmd);
fprintf('   - 熵减少: %.2f%%\n', (entropy_original - entropy_stvmd)/entropy_original * 100);
fprintf('   - 原始图像对比度: %.4f\n', contrast_original);
fprintf('   - 增强STVMD处理后对比度: %.4f\n', contrast_stvmd);
fprintf('   - 对比度提升: %.2f%%\n', (contrast_stvmd - contrast_original)/contrast_original * 100);
fprintf('   - 原始PSLR: %.2f dB\n', pslr_original);
fprintf('   - 增强STVMD处理后PSLR: %.2f dB\n', pslr_stvmd);
fprintf('   - PSLR改善: %.2f dB\n', pslr_stvmd - pslr_original);
fprintf('   - 原始ISLR: %.2f dB\n', islr_original);
fprintf('   - 增强STVMD处理后ISLR: %.2f dB\n', islr_stvmd);
fprintf('   - ISLR改善: %.2f dB\n', islr_stvmd - islr_original);

% 显示自适应参数统计
if params.use_adaptive_params
    fprintf('\n8. 自适应参数统计:\n');
    fprintf('   - 平均SNR: %.2f dB\n', mean(analysis_results.snr_estimates));
    fprintf('   - SNR范围: %.2f - %.2f dB\n', min(analysis_results.snr_estimates), max(analysis_results.snr_estimates));
    fprintf('   - 平均信号熵: %.4f\n', mean(analysis_results.entropy_values));
    fprintf('   - 模式数量范围: %d - %d\n', min(sum(analysis_results.mode_energies > 0, 2)), max(sum(analysis_results.mode_energies > 0, 2)));
end

%% 6. 额外的分析图
% 选择一个代表性的距离单元
r_idx_analyze = round(Nr/2);

% 相位误差分析
figure('Name', '相位误差分析', 'Position', [100, 100, 800, 600]);
subplot(2,1,1);
imagesc(phase_errors);
colorbar;
xlabel('慢时间采样点');
ylabel('距离单元');
title('估计的相位误差分布');
colormap('jet');

subplot(2,1,2);
plot(phase_errors(r_idx_analyze, :), 'LineWidth', 2);
xlabel('慢时间采样点');
ylabel('相位误差 (rad)');
title(['距离单元 ' num2str(r_idx_analyze) ' 的相位误差']);
grid on;

% 模式分解分析
figure('Name', '模式分解分析', 'Position', [200, 100, 1000, 600]);
modes = decomposed_modes{r_idx_analyze};
K = size(modes, 1);

for k = 1:min(K, 3) % 最多显示3个模式
    subplot(3, 2, 2*k-1);
    plot(real(modes(k, :)), 'LineWidth', 1.5);
    xlabel('慢时间采样点');
    ylabel('幅度');
    title(['模式 ' num2str(k) ' 实部 (距离单元 ' num2str(r_idx_analyze) ')']);
    grid on;

    subplot(3, 2, 2*k);
    IF = analysis_results.instantaneous_freq{r_idx_analyze}{k};
    plot(IF, 'LineWidth', 1.5);
    xlabel('慢时间采样点');
    ylabel('归一化频率');
    title(['模式 ' num2str(k) ' 瞬时频率']);
    grid on;
end

% 高质量的成像结果对比
figure('Name', '成像结果对比', 'Position', [300, 100, 1200, 500]);

% 原始图像
subplot(1, 2, 1);
imagesc(ISAR_original_db);
caxis([-30, 0]);
colorbar;
xlabel('方位单元');
ylabel('距离单元');
title('原始ISAR图像');
colormap('jet');
axis xy;

% STVMD处理后的图像
subplot(1, 2, 2);
ISAR_stvmd_db = 20*log10(abs(ISAR_image)/max(abs(ISAR_image(:))));
imagesc(ISAR_stvmd_db);
caxis([-30, 0]);
colorbar;
xlabel('方位单元');
ylabel('距离单元');
title('STVMD处理后的ISAR图像');
colormap('jet');
axis xy;

fprintf('\n=== 测试完成 ===\n');

%% 辅助函数定义
function entropy = calculate_image_entropy(image)
    % 归一化图像
    img_norm = image / sum(image(:));

    % 计算熵
    entropy = -sum(img_norm(:) .* log2(img_norm(:) + eps));
end

function contrast = calculate_image_contrast(image)
    % 计算图像对比度（标准差/均值）
    contrast = std(image(:)) / mean(image(:));
end

function pslr = calculate_pslr(image)
    % 计算峰值旁瓣比 (Peak Sidelobe Ratio)
    % 找到主瓣峰值
    [max_val, max_idx] = max(image(:));
    [max_r, max_a] = ind2sub(size(image), max_idx);

    % 创建主瓣掩码（简化处理）
    mainlobe_size = 3; % 主瓣半径
    mask = false(size(image));
    r_range = max(1, max_r-mainlobe_size):min(size(image,1), max_r+mainlobe_size);
    a_range = max(1, max_a-mainlobe_size):min(size(image,2), max_a+mainlobe_size);
    mask(r_range, a_range) = true;

    % 计算旁瓣最大值
    sidelobe_image = image;
    sidelobe_image(mask) = 0;
    max_sidelobe = max(sidelobe_image(:));

    % PSLR = 20*log10(主瓣峰值/最大旁瓣)
    pslr = 20 * log10(max_val / (max_sidelobe + eps));
end

function islr = calculate_islr(image)
    % 计算积分旁瓣比 (Integrated Sidelobe Ratio)
    % 找到主瓣峰值
    [max_val, max_idx] = max(image(:));
    [max_r, max_a] = ind2sub(size(image), max_idx);

    % 创建主瓣掩码
    mainlobe_size = 3;
    mask = false(size(image));
    r_range = max(1, max_r-mainlobe_size):min(size(image,1), max_r+mainlobe_size);
    a_range = max(1, max_a-mainlobe_size):min(size(image,2), max_a+mainlobe_size);
    mask(r_range, a_range) = true;

    % 计算主瓣能量和旁瓣能量
    mainlobe_energy = sum(image(mask).^2);
    sidelobe_energy = sum(image(~mask).^2);

    % ISLR = 10*log10(旁瓣能量/主瓣能量)
    islr = 10 * log10(sidelobe_energy / (mainlobe_energy + eps));
end