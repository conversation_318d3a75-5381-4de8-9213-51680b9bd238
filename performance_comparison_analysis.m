function performance_comparison_analysis()
% 增强STVMD-ISAR算法性能对比分析
% 对比原始算法、传统STVMD算法和增强STVMD算法的性能

fprintf('=== 增强STVMD-ISAR算法性能对比分析 ===\n\n');

%% 1. 数据准备
fprintf('1. 准备测试数据...\n');

% 生成仿真数据
use_simulation = true;
if use_simulation
    fprintf('   使用仿真数据进行测试\n');
    % 调用仿真数据生成函数
    ISARrot_trans;
    load('ISAR_data.mat', 's_r_tm');
else
    fprintf('   使用实测数据进行测试\n');
    % 这里应该加载实测数据
    load('real_ship_data.mat', 's_r_tm');
end

[Nr, Ntm] = size(s_r_tm);
fprintf('   数据尺寸: %d x %d (距离 x 慢时间)\n', Nr, Ntm);

% 添加噪声
SNR_dB = 15;
noise_power = var(s_r_tm(:)) / (10^(SNR_dB/10));
noise = sqrt(noise_power/2) * (randn(size(s_r_tm)) + 1j*randn(size(s_r_tm)));
s_r_tm_noisy = s_r_tm + noise;
fprintf('   添加噪声，SNR = %d dB\n', SNR_dB);

%% 2. 算法对比测试
fprintf('\n2. 执行算法对比测试...\n');

% 方法1：原始FFT成像（基准）
fprintf('   方法1: 原始FFT成像...\n');
tic;
ISAR_original = fftshift(fft(s_r_tm_noisy, Ntm, 2), 2);
time_original = toc;

% 方法2：传统STVMD算法
fprintf('   方法2: 传统STVMD算法...\n');
params_traditional = struct();
params_traditional.K = 3;
params_traditional.alpha = 2000;
params_traditional.tau = 0.1;
params_traditional.tol = 1e-7;
params_traditional.max_iter = 300;
params_traditional.window_length = 64;
params_traditional.overlap = 0.75;
% 禁用所有增强功能
params_traditional.use_adaptive_params = false;
params_traditional.use_enhanced_phase_estimation = false;
params_traditional.use_sidelobe_suppression = false;
params_traditional.use_keystone = false;

tic;
[ISAR_traditional, ~, ~, results_traditional] = STVMD_ISAR_Complex_Motion(s_r_tm_noisy, params_traditional);
time_traditional = toc;

% 方法3：增强STVMD算法
fprintf('   方法3: 增强STVMD算法...\n');
params_enhanced = struct();
params_enhanced.K = 5;
params_enhanced.alpha = 5000;
params_enhanced.tau = 0.1;
params_enhanced.tol = 1e-8;
params_enhanced.max_iter = 500;
params_enhanced.window_length = 128;
params_enhanced.overlap = 0.85;
% 启用所有增强功能
params_enhanced.use_adaptive_params = true;
params_enhanced.use_enhanced_phase_estimation = true;
params_enhanced.use_sidelobe_suppression = true;
params_enhanced.sidelobe_method = 'adaptive';
params_enhanced.use_keystone = false;
params_enhanced.entropy_threshold = 0.1;

tic;
[ISAR_enhanced, ~, ~, results_enhanced] = STVMD_ISAR_Complex_Motion(s_r_tm_noisy, params_enhanced);
time_enhanced = toc;

%% 3. 性能指标计算
fprintf('\n3. 计算性能指标...\n');

% 图像熵
entropy_original = calculate_image_entropy(abs(ISAR_original));
entropy_traditional = calculate_image_entropy(abs(ISAR_traditional));
entropy_enhanced = calculate_image_entropy(abs(ISAR_enhanced));

% 图像对比度
contrast_original = calculate_image_contrast(abs(ISAR_original));
contrast_traditional = calculate_image_contrast(abs(ISAR_traditional));
contrast_enhanced = calculate_image_contrast(abs(ISAR_enhanced));

% 峰值旁瓣比 (PSLR)
pslr_original = calculate_pslr(abs(ISAR_original));
pslr_traditional = calculate_pslr(abs(ISAR_traditional));
pslr_enhanced = calculate_pslr(abs(ISAR_enhanced));

% 积分旁瓣比 (ISLR)
islr_original = calculate_islr(abs(ISAR_original));
islr_traditional = calculate_islr(abs(ISAR_traditional));
islr_enhanced = calculate_islr(abs(ISAR_enhanced));

% 聚焦度量（基于图像梯度）
focus_original = calculate_focus_measure(abs(ISAR_original));
focus_traditional = calculate_focus_measure(abs(ISAR_traditional));
focus_enhanced = calculate_focus_measure(abs(ISAR_enhanced));

%% 4. 结果显示
fprintf('\n4. 性能对比结果:\n');
fprintf('=' * ones(1, 80)); fprintf('\n');
fprintf('%-20s | %-12s | %-12s | %-12s\n', '性能指标', '原始FFT', '传统STVMD', '增强STVMD');
fprintf('=' * ones(1, 80)); fprintf('\n');
fprintf('%-20s | %12.4f | %12.4f | %12.4f\n', '图像熵', entropy_original, entropy_traditional, entropy_enhanced);
fprintf('%-20s | %12.4f | %12.4f | %12.4f\n', '对比度', contrast_original, contrast_traditional, contrast_enhanced);
fprintf('%-20s | %12.2f | %12.2f | %12.2f\n', 'PSLR (dB)', pslr_original, pslr_traditional, pslr_enhanced);
fprintf('%-20s | %12.2f | %12.2f | %12.2f\n', 'ISLR (dB)', islr_original, islr_traditional, islr_enhanced);
fprintf('%-20s | %12.4f | %12.4f | %12.4f\n', '聚焦度量', focus_original, focus_traditional, focus_enhanced);
fprintf('%-20s | %12.2f | %12.2f | %12.2f\n', '处理时间 (s)', time_original, time_traditional, time_enhanced);
fprintf('=' * ones(1, 80)); fprintf('\n');

% 改进百分比
fprintf('\n5. 改进效果分析:\n');
fprintf('传统STVMD相对于原始FFT的改进:\n');
fprintf('   - 图像熵减少: %.2f%%\n', (entropy_original - entropy_traditional)/entropy_original * 100);
fprintf('   - 对比度提升: %.2f%%\n', (contrast_traditional - contrast_original)/contrast_original * 100);
fprintf('   - PSLR改善: %.2f dB\n', pslr_traditional - pslr_original);
fprintf('   - ISLR改善: %.2f dB\n', islr_traditional - islr_original);
fprintf('   - 聚焦度量提升: %.2f%%\n', (focus_traditional - focus_original)/focus_original * 100);

fprintf('\n增强STVMD相对于原始FFT的改进:\n');
fprintf('   - 图像熵减少: %.2f%%\n', (entropy_original - entropy_enhanced)/entropy_original * 100);
fprintf('   - 对比度提升: %.2f%%\n', (contrast_enhanced - contrast_original)/contrast_original * 100);
fprintf('   - PSLR改善: %.2f dB\n', pslr_enhanced - pslr_original);
fprintf('   - ISLR改善: %.2f dB\n', islr_enhanced - islr_original);
fprintf('   - 聚焦度量提升: %.2f%%\n', (focus_enhanced - focus_original)/focus_original * 100);

fprintf('\n增强STVMD相对于传统STVMD的改进:\n');
fprintf('   - 图像熵减少: %.2f%%\n', (entropy_traditional - entropy_enhanced)/entropy_traditional * 100);
fprintf('   - 对比度提升: %.2f%%\n', (contrast_enhanced - contrast_traditional)/contrast_traditional * 100);
fprintf('   - PSLR改善: %.2f dB\n', pslr_enhanced - pslr_traditional);
fprintf('   - ISLR改善: %.2f dB\n', islr_enhanced - islr_traditional);
fprintf('   - 聚焦度量提升: %.2f%%\n', (focus_enhanced - focus_traditional)/focus_traditional * 100);

%% 6. 可视化对比
fprintf('\n6. 生成对比图表...\n');
create_comparison_plots(ISAR_original, ISAR_traditional, ISAR_enhanced, ...
                       entropy_original, entropy_traditional, entropy_enhanced, ...
                       contrast_original, contrast_traditional, contrast_enhanced, ...
                       pslr_original, pslr_traditional, pslr_enhanced, ...
                       islr_original, islr_traditional, islr_enhanced, ...
                       focus_original, focus_traditional, focus_enhanced);

fprintf('\n=== 性能对比分析完成 ===\n');

end

%% 辅助函数

function entropy = calculate_image_entropy(image)
    % 计算图像熵
    image_norm = image / sum(image(:));
    entropy = -sum(image_norm(:) .* log2(image_norm(:) + eps));
end

function contrast = calculate_image_contrast(image)
    % 计算图像对比度
    contrast = std(image(:)) / mean(image(:));
end

function pslr = calculate_pslr(image)
    % 计算峰值旁瓣比
    [max_val, max_idx] = max(image(:));
    [max_r, max_a] = ind2sub(size(image), max_idx);

    mainlobe_size = 3;
    mask = false(size(image));
    r_range = max(1, max_r-mainlobe_size):min(size(image,1), max_r+mainlobe_size);
    a_range = max(1, max_a-mainlobe_size):min(size(image,2), max_a+mainlobe_size);
    mask(r_range, a_range) = true;

    sidelobe_image = image;
    sidelobe_image(mask) = 0;
    max_sidelobe = max(sidelobe_image(:));

    pslr = 20 * log10(max_val / (max_sidelobe + eps));
end

function islr = calculate_islr(image)
    % 计算积分旁瓣比
    [max_val, max_idx] = max(image(:));
    [max_r, max_a] = ind2sub(size(image), max_idx);

    mainlobe_size = 3;
    mask = false(size(image));
    r_range = max(1, max_r-mainlobe_size):min(size(image,1), max_r+mainlobe_size);
    a_range = max(1, max_a-mainlobe_size):min(size(image,2), max_a+mainlobe_size);
    mask(r_range, a_range) = true;

    mainlobe_energy = sum(image(mask).^2);
    sidelobe_energy = sum(image(~mask).^2);

    islr = 10 * log10(sidelobe_energy / (mainlobe_energy + eps));
end

function focus = calculate_focus_measure(image)
    % 计算聚焦度量（基于图像梯度）
    [Gx, Gy] = gradient(image);
    focus = mean(sqrt(Gx(:).^2 + Gy(:).^2));
end

function create_comparison_plots(ISAR_original, ISAR_traditional, ISAR_enhanced, ...
                                entropy_original, entropy_traditional, entropy_enhanced, ...
                                contrast_original, contrast_traditional, contrast_enhanced, ...
                                pslr_original, pslr_traditional, pslr_enhanced, ...
                                islr_original, islr_traditional, islr_enhanced, ...
                                focus_original, focus_traditional, focus_enhanced)
    % 创建对比图表

    % 图1: ISAR图像对比
    figure('Position', [100, 100, 1500, 500], 'Name', 'ISAR成像结果对比');

    % 原始FFT成像
    subplot(1, 3, 1);
    imagesc(20*log10(abs(ISAR_original)/max(abs(ISAR_original(:)))));
    caxis([-40, 0]);
    colorbar;
    title('原始FFT成像');
    xlabel('方位单元');
    ylabel('距离单元');
    colormap('jet');
    axis xy;

    % 传统STVMD成像
    subplot(1, 3, 2);
    imagesc(20*log10(abs(ISAR_traditional)/max(abs(ISAR_traditional(:)))));
    caxis([-40, 0]);
    colorbar;
    title('传统STVMD成像');
    xlabel('方位单元');
    ylabel('距离单元');
    colormap('jet');
    axis xy;

    % 增强STVMD成像
    subplot(1, 3, 3);
    imagesc(20*log10(abs(ISAR_enhanced)/max(abs(ISAR_enhanced(:)))));
    caxis([-40, 0]);
    colorbar;
    title('增强STVMD成像');
    xlabel('方位单元');
    ylabel('距离单元');
    colormap('jet');
    axis xy;

    % 图2: 性能指标对比
    figure('Position', [200, 150, 1200, 800], 'Name', '性能指标对比');

    % 图像熵对比
    subplot(2, 3, 1);
    bar([entropy_original, entropy_traditional, entropy_enhanced]);
    set(gca, 'XTickLabel', {'原始FFT', '传统STVMD', '增强STVMD'});
    ylabel('图像熵');
    title('图像熵对比 (越低越好)');
    grid on;

    % 对比度对比
    subplot(2, 3, 2);
    bar([contrast_original, contrast_traditional, contrast_enhanced]);
    set(gca, 'XTickLabel', {'原始FFT', '传统STVMD', '增强STVMD'});
    ylabel('对比度');
    title('图像对比度对比 (越高越好)');
    grid on;

    % PSLR对比
    subplot(2, 3, 3);
    bar([pslr_original, pslr_traditional, pslr_enhanced]);
    set(gca, 'XTickLabel', {'原始FFT', '传统STVMD', '增强STVMD'});
    ylabel('PSLR (dB)');
    title('峰值旁瓣比对比 (越高越好)');
    grid on;

    % ISLR对比
    subplot(2, 3, 4);
    bar([islr_original, islr_traditional, islr_enhanced]);
    set(gca, 'XTickLabel', {'原始FFT', '传统STVMD', '增强STVMD'});
    ylabel('ISLR (dB)');
    title('积分旁瓣比对比 (越低越好)');
    grid on;

    % 聚焦度量对比
    subplot(2, 3, 5);
    bar([focus_original, focus_traditional, focus_enhanced]);
    set(gca, 'XTickLabel', {'原始FFT', '传统STVMD', '增强STVMD'});
    ylabel('聚焦度量');
    title('聚焦度量对比 (越高越好)');
    grid on;

    % 综合性能雷达图
    subplot(2, 3, 6);
    % 归一化指标（使增强STVMD为基准1）
    entropy_norm = [entropy_original/entropy_enhanced, entropy_traditional/entropy_enhanced, 1];
    contrast_norm = [contrast_original/contrast_enhanced, contrast_traditional/contrast_enhanced, 1];
    pslr_norm = [pslr_original/pslr_enhanced, pslr_traditional/pslr_enhanced, 1];
    focus_norm = [focus_original/focus_enhanced, focus_traditional/focus_enhanced, 1];

    % 对于熵值，取倒数（因为越低越好）
    entropy_norm = 1 ./ entropy_norm;

    performance_matrix = [entropy_norm; contrast_norm; pslr_norm; focus_norm];

    plot(performance_matrix', 'LineWidth', 2, 'Marker', 'o', 'MarkerSize', 8);
    set(gca, 'XTickLabel', {'图像熵', '对比度', 'PSLR', '聚焦度量'});
    ylabel('归一化性能 (增强STVMD=1)');
    title('综合性能对比');
    legend({'原始FFT', '传统STVMD', '增强STVMD'}, 'Location', 'best');
    grid on;

    % 图3: 剖面对比
    figure('Position', [300, 200, 1000, 600], 'Name', '剖面对比分析');

    [Nr, Ntm] = size(ISAR_original);

    % 距离向剖面对比
    subplot(2, 2, 1);
    range_profile_original = abs(ISAR_original(:, round(Ntm/2)));
    range_profile_traditional = abs(ISAR_traditional(:, round(Ntm/2)));
    range_profile_enhanced = abs(ISAR_enhanced(:, round(Ntm/2)));

    plot(range_profile_original, 'b-', 'LineWidth', 1.5, 'DisplayName', '原始FFT');
    hold on;
    plot(range_profile_traditional, 'r-', 'LineWidth', 1.5, 'DisplayName', '传统STVMD');
    plot(range_profile_enhanced, 'g-', 'LineWidth', 1.5, 'DisplayName', '增强STVMD');
    xlabel('距离单元');
    ylabel('幅度');
    title('距离向剖面对比');
    legend('show');
    grid on;

    % 方位向剖面对比
    subplot(2, 2, 2);
    azimuth_profile_original = abs(ISAR_original(round(Nr/2), :));
    azimuth_profile_traditional = abs(ISAR_traditional(round(Nr/2), :));
    azimuth_profile_enhanced = abs(ISAR_enhanced(round(Nr/2), :));

    plot(azimuth_profile_original, 'b-', 'LineWidth', 1.5, 'DisplayName', '原始FFT');
    hold on;
    plot(azimuth_profile_traditional, 'r-', 'LineWidth', 1.5, 'DisplayName', '传统STVMD');
    plot(azimuth_profile_enhanced, 'g-', 'LineWidth', 1.5, 'DisplayName', '增强STVMD');
    xlabel('方位单元');
    ylabel('幅度');
    title('方位向剖面对比');
    legend('show');
    grid on;

    % 距离向剖面对比（dB）
    subplot(2, 2, 3);
    plot(20*log10(range_profile_original/max(range_profile_original)), 'b-', 'LineWidth', 1.5, 'DisplayName', '原始FFT');
    hold on;
    plot(20*log10(range_profile_traditional/max(range_profile_traditional)), 'r-', 'LineWidth', 1.5, 'DisplayName', '传统STVMD');
    plot(20*log10(range_profile_enhanced/max(range_profile_enhanced)), 'g-', 'LineWidth', 1.5, 'DisplayName', '增强STVMD');
    xlabel('距离单元');
    ylabel('归一化幅度 (dB)');
    title('距离向剖面对比 (dB)');
    legend('show');
    grid on;
    ylim([-50, 0]);

    % 方位向剖面对比（dB）
    subplot(2, 2, 4);
    plot(20*log10(azimuth_profile_original/max(azimuth_profile_original)), 'b-', 'LineWidth', 1.5, 'DisplayName', '原始FFT');
    hold on;
    plot(20*log10(azimuth_profile_traditional/max(azimuth_profile_traditional)), 'r-', 'LineWidth', 1.5, 'DisplayName', '传统STVMD');
    plot(20*log10(azimuth_profile_enhanced/max(azimuth_profile_enhanced)), 'g-', 'LineWidth', 1.5, 'DisplayName', '增强STVMD');
    xlabel('方位单元');
    ylabel('归一化幅度 (dB)');
    title('方位向剖面对比 (dB)');
    legend('show');
    grid on;
    ylim([-50, 0]);

end
